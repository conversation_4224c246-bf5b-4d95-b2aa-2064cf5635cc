package com.udaan.orderform.common.models

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.constraint.models.dtos.ItemLevelDeliveryCharge
import com.udaan.constraint.models.dtos.LogChargeEntityType

data class LogCharge(
    val deliveryChargesInPaise: Long,
    val logChargeEntityType: LogChargeEntityType = LogChargeEntityType.DEFAULT,
    val referenceSlab: SlabMetadata? = null,
    val itemLevelLogCharge: List<ItemLevelDeliveryCharge>,
    val chargePayer: LogChargePayer,
    val isApplicable: Boolean
)

data class OrgUnitLogCharge(
    val orgUnitId: String,
    val logCharge: LogCharge
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = OrderDeliveryChargeRange::class, name = "VALUE_BASED"),
    JsonSubTypes.Type(value = OrderDeliveryChargeRangePercentage::class, name = "PERCENTAGE_BASED")
)
sealed class SlabMetadata(type: EvalParamUnitType)

enum class EvalParamDto {
    ORDER_VALUE_IN_PAISE, TONNAGE_IN_GM
}

enum class EvalParamUnitType {
    VALUE_BASED, PERCENTAGE_BASED
}

data class OrderDeliveryChargeRange(
    val evalParam: EvalParamDto,
    val minValue: Long,
    val maxValue: Long?,
    val deliveryChargesInPaise: Long,
    val hasMax: Boolean
) : SlabMetadata(EvalParamUnitType.VALUE_BASED)

data class OrderDeliveryChargeRangePercentage(
    val evalParam: EvalParamDto,
    val minValue: Long,
    val maxValue: Long?,
    val deliveryChargePercentageValueInBps: Int,
    val hasMax: Boolean
) : SlabMetadata(EvalParamUnitType.PERCENTAGE_BASED)

enum class LogChargePayer {
    BUYER, SELLER
}