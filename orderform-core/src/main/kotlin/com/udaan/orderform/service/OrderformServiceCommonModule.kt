package com.udaan.orderform.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.inject.*
import com.google.inject.TypeLiteral
import com.google.inject.name.Named
import com.google.inject.name.Names
import com.google.protobuf.util.JsonFormat
import com.google.protobuf.util.ProtoJacksonModule
import com.microsoft.azure.eventhubs.EventHubClient
import com.udaan.cart.common.LogisticsProvider
import com.udaan.cart.common.LogisticsProviderImpl
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.client.ComboClient
import com.udaan.catalog.client.RedisListingRepository
import com.udaan.catalog.client.combos.CatalogCombosClientV2
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.chat.client.ChatServiceClient
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import com.udaan.common.comm.SmsProviders
import com.udaan.common.comm.UdaanSmsService
import com.udaan.common.comm.UdaanSmsServiceConfig
import com.udaan.common.server.PrometheusClient
import com.udaan.common.server.UdaanServerConfig
import com.udaan.communicationframework.client.NotificationClient
import com.udaan.compliance.client.ComplianceServiceClient
import com.udaan.config.client.BusinessConfigClient
import com.udaan.config.client.ConfigClientFactory
import com.udaan.constraint.client.ConstraintClient
import com.udaan.credit.client.CreditRepository
import com.udaan.credit.client.CreditServiceClient
import com.udaan.dropslot.client.DropslotServiceClient
import com.udaan.featurestore.client.FeatureStoreClient
import com.udaan.fulfilment.client.ATPOrchestrationClient
import com.udaan.fulfilment.client.FulfilmentOrchestrationServiceClient
import com.udaan.fulfilment.client.ManagedFulfillmentServiceClient
import com.udaan.fulfilmentcatalog.client.ProductMasterServiceClient
import com.udaan.fulfilmentcatalog.client.RedisProductMasterRepository
import com.udaan.id.IdBlockAdvanceStore
import com.udaan.id.IdentityGenerator
import com.udaan.id.SqlServerBlockAdvanceStore
import com.udaan.insights.client.BuyerProfileClient
import com.udaan.inventory.client.v2.InventoryReservationClient
import com.udaan.inventory.client.v2.InventoryServiceClient
import com.udaan.invoicing.client.InvoicingServiceClient
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.listingtag.client.ListingTaggingClient
import com.udaan.logistics.client.LogisticsConsoleServiceClient
import com.udaan.logistics.client.LogisticsFPJITServiceabilityClient
import com.udaan.logistics.client.LogisticsVSLClient
import com.udaan.logistics.promise.db.PincodeMasterDao
import com.udaan.mariodistribution.client.MarioDistributionClient
import com.udaan.orchestrator.client.*
import com.udaan.order_mgt.client.LogisticsServiceClient
import com.udaan.order_mgt.client.SellerOrderServiceClient
import com.udaan.orderform.cart.common.providers.*
import com.udaan.orderform.common.providers.interpreters.LotBasedPriceInterpreter
import com.udaan.orderform.common.providers.interpreters.LotSchemePriceInterpreter
import com.udaan.orderform.common.providers.interpreters.PriceContextInterpreter
import com.udaan.orderform.common.providers.interpreters.PriceContextInterpreterImpl
import com.udaan.orderform.cart.context.ContextFactory
import com.udaan.orderform.cart.context.ContextFactoryImpl
import com.udaan.orderform.cart.db.dao.ReservationInfoDao
import com.udaan.orderform.cart.db.dao.ReservationInfoDaoImpl
import com.udaan.orderform.cart.db.repo.*
import com.udaan.orderform.cart.domain.business.maxunit.MaxUnit
import com.udaan.orderform.cart.domain.business.maxunit.MaxUnitImpl
import com.udaan.orderform.cart.domain.business.moq.Moq
import com.udaan.orderform.cart.domain.business.moq.MoqImpl
import com.udaan.orderform.cart.domain.business.mov.*
import com.udaan.orderform.cart.domain.business.validations.BusinessValidator
import com.udaan.orderform.cart.domain.business.validations.BusinessValidatorImpl
import com.udaan.orderform.cart.metrics.OrderFormEventTracker
import com.udaan.orderform.cart.metrics.OrderFormEventTrackerImpl
import com.udaan.orderform.cart.models.dto.*
import com.udaan.orderform.cart.operations.order.lifecycles.v2.*
import com.udaan.orderform.cart.operations.order.lifecycles.v2.impl.*
import com.udaan.orderform.cart.selectors.order.DefaultOrderFormSelector
import com.udaan.orderform.cart.selectors.order.HorecaOrderFormSelector
import com.udaan.orderform.cart.selectors.order.OrderFormSelector
import com.udaan.orderform.cart.services.v2.*
import com.udaan.orderform.cart.services.v2.responses.*
import com.udaan.orderform.cart.services.v2.responses.DefaultExceptionHandlerFactory
import com.udaan.orderform.cart.services.v2.responses.DefaultResponseBuilder
import com.udaan.orderform.cart.services.v2.responses.ExceptionHandlerFactory
import com.udaan.orderform.cart.services.v2.responses.PacmanResponseBuilder
import com.udaan.orderform.common.metrics.EventTracker
import com.udaan.orderform.common.metrics.EventTrackerImpl
import com.udaan.orderform.common.providers.*
import com.udaan.orderform.constants.DIConstants
import com.udaan.orderform.dal.repository.InvoiceRepository
import com.udaan.orderform.dal.repository.SellerOrderRepository
import com.udaan.orderform.service.business.promotion.PromoManager
import com.udaan.orderform.service.business.promotion.PromoManagerImpl
import com.udaan.payments.client.PaymentServiceClient
import com.udaan.pricing.LotPricingClient
import com.udaan.pricing.PricingClient
import com.udaan.pricing_options.core.PharmaPricingOption
import com.udaan.pricing_options.core.PricingOptionsClientV2
import com.udaan.promotions.client.PromotionsServiceClient
import com.udaan.resources.RedisLettuce6Client
import com.udaan.resources.ResourceBuilder
import com.udaan.rewards.client.RewardsClient
import com.udaan.scnetwork.client.userFacilityEdge.BuyerHubMappingCacheClient
import com.udaan.storage.client.StorageServiceClient
import com.udaan.subscription.client.SubscriptionBuyerClient
import com.udaan.tradecredit.client.TradeCreditClient
import com.udaan.tradequality.client.OrderProfilingClient
import com.udaan.tradequality.client.RiskProfileClient
import com.udaan.tradequality.client.TradeQualityBuyerRiskClient
import com.udaan.user.client.*
import com.udaan.warehouse.client.WarehouseClientV2
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.Handle
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.function.Consumer
import org.jdbi.v3.sqlobject.SqlObjectPlugin
import org.jdbi.v3.core.kotlin.KotlinPlugin
import org.jdbi.v3.sqlobject.kotlin.KotlinSqlObjectPlugin

open class OrderformServiceCommonModule(private val closeableListener: Consumer<AutoCloseable>) : AbstractModule() {

    private val cacheMetrics = PrometheusClient.cacheMetricsCollector

    fun dbi(): Jdbi {
        return ResourceBuilder.jdbi3("order-form").build().apply {
            installPlugin(SqlObjectPlugin())
            installPlugin(KotlinPlugin())
            installPlugin(KotlinSqlObjectPlugin())
        }
    }

    @Named("order-readonly")
    @Provides
    @Singleton
    fun readOnlyDbi(): Jdbi {
        return ResourceBuilder.jdbi3("order-mgmt/orders-readonly").build().apply {
            installPlugin(SqlObjectPlugin())
            installPlugin(KotlinPlugin())
            installPlugin(KotlinSqlObjectPlugin())
        }
    }

    open fun getInvoicingClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["invoicing-service"]!!
    }

    open fun getCatalogClientConfig(): UdaanClientConfig {
        val catalogConfig: UdaanClientConfig = UdaanServerConfig["resiliency-catalog-read-path"]!!
        return catalogConfig
    }

    open fun getRedisListingRepositoryConfig(): UdaanClientConfig {
        return UdaanServerConfig["resiliency-catalog-read-path"]!!
    }

    open fun getMarioDistributionClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["mario-distribution-service"]!!
    }

    open fun getPricingClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["resiliency-price-read-path"]!!
    }

    open fun getFeatureStoreClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["feature-store-service"]!!
    }

    open fun getUserClientConfig(): UdaanClientConfig {
        val userConfig: UdaanClientConfig = UdaanServerConfig["user"]!!
        return userConfig
    }

    open fun getOrgClientConfig(): UdaanClientConfig {
        val userConfig: UdaanClientConfig = UdaanServerConfig["user"]!!
        return userConfig
    }

    open fun getTradeQualityConfig(): UdaanClientConfig {
        val tqConfig: UdaanClientConfig = UdaanServerConfig["trade-quality-service"]!!
        return tqConfig
    }

    @Singleton
    @Provides
    private fun getConstraintsClient(): ConstraintClient = makeClient("constraint-service")

    @Singleton
    @Provides
    private fun makeDropslotServiceClient(): DropslotServiceClient = makeClient("dropslot-service")

    open fun getStorageServiceClientConfig(): UdaanClientConfig {
        val storageConfig: UdaanClientConfig = UdaanServerConfig["storage"]!!
        return storageConfig
    }

    open fun getPaymentsServiceClientConfig(): UdaanClientConfig {
        val paymentsServiceConfig: UdaanClientConfig = UdaanServerConfig["paymentsService"]!!
        return paymentsServiceConfig
    }

    open fun getWarehouseServiceClientConfig(): UdaanClientConfig {
        val warehouseServiceConfig: UdaanClientConfig = UdaanServerConfig["warehouseService"]!!
        return warehouseServiceConfig
    }

    open fun getChatClientConfig(): UdaanClientConfig {
        val chatServiceConfig: UdaanClientConfig = UdaanServerConfig["chat"]!!
        return chatServiceConfig
    }

    open fun getCreditServiceClientConfig(): UdaanClientConfig {
        val creditConfig: UdaanClientConfig = UdaanServerConfig["credit"]!!
        return creditConfig
    }

    open fun getFulfilmentClientConfig(): UdaanClientConfig {
        val fulfilmentConfig: UdaanClientConfig = UdaanServerConfig["fulfillment-service"]!!
        return fulfilmentConfig
    }

    open fun getFulfilmentCatalogClientConfig(): UdaanClientConfig {
        val fulfilmentConfig: UdaanClientConfig = UdaanServerConfig["fulfillment-catalog-service"]!!
        return fulfilmentConfig
    }

    open fun getOrchestratorClientConfig(): UdaanClientConfig {
        val orchestratorConfig: UdaanClientConfig = UdaanServerConfig["orchestrator-service"]!!
        return orchestratorConfig
    }

    open fun getReservationClientConfig(): UdaanClientConfig {
        val reservationConfig: UdaanClientConfig = UdaanServerConfig["orchestrator-reservation-service"]!!
        return reservationConfig
    }

    open fun getFulfilmentReadPathClientConfig(): UdaanClientConfig {
        val fulfilmentConfig: UdaanClientConfig = UdaanServerConfig["resiliency-fulfillment-service-read-path"]!!
        return fulfilmentConfig
    }

    open fun getPromotionsClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["resiliency-promotions-service"]!!
    }

    open fun getInventoryServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["inventory-service"]!!
    }

    open fun getInventoryReservationServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["inventory-reservation-service"]!!
    }

    open fun getLogisticsConsoleServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["logistics-console-service"]!!
    }

    open fun getRewardsServiceConfig(): UdaanClientConfig {
        return UdaanServerConfig["rewards-service"]!!
    }

    open fun getNotificationServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["notification-service"]!!
    }

    open fun getCommunicationServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["communication-framework-service"]!!
    }

    open fun getComplianceServiceClientConfig(): UdaanClientConfig {
        return UdaanServerConfig["compliance-service"]!!
    }

    open fun getTradeCreditClientConfig(): UdaanClientConfig {
        val creditConfig: UdaanClientConfig = UdaanServerConfig["trade-credit-service"]!!
        return creditConfig
    }

    override fun configure() {
        val dbiInstance = dbi()
        bind(Jdbi::class.java).toInstance(dbiInstance)
        bind(Handle::class.java).toInstance(dbiInstance.open())
        bind(JsonFormat.Printer::class.java).toInstance(protoJsonPrinter())
        bind(JsonFormat.Parser::class.java).toInstance(protoJsonParser())

        bind(SellerOrderRepository::class.java).toInstance(dbiInstance.onDemand(SellerOrderRepository::class.java))
        bind(InvoiceRepository::class.java).toInstance(dbiInstance.onDemand(InvoiceRepository::class.java))
        bind(com.udaan.om.dal.FulfillmentRepository::class.java).toInstance(dbiInstance.onDemand(com.udaan.om.dal.FulfillmentRepository::class.java))

        bind(UdaanSmsService::class.java).toInstance(
            UdaanSmsService(
                UdaanSmsServiceConfig.DEFAULT.copy(
                    providerOrdering = listOf(
                        SmsProviders.KALEYRA_TRANS,
                        SmsProviders.VALUEFIRST_TRANS,
                        SmsProviders.EXOTEL
                    )
                )
            )
        )

        bind(ObjectMapper::class.java).toInstance(
            ObjectMapper().registerKotlinModule().registerModule(ProtoJacksonModule())
        )

        bind(PincodeMasterDao::class.java).toInstance(dbiInstance.onDemand(PincodeMasterDao::class.java))

        val readOnlyDbi = readOnlyDbi()
        bind(Jdbi::class.java).annotatedWith(Names.named("readOnlyDbi")).toInstance(readOnlyDbi)
        bind(EventTracker::class.java).to(EventTrackerImpl::class.java)
        bind(Mov::class.java).to(MovImpl::class.java)
        bind(Mov::class.java)
            .annotatedWith(Names.named("horeca_mov_calculator"))
            .to(HorecaMovImpl::class.java)
        bind(Mov::class.java)
            .annotatedWith(Names.named("loose_item_mov_calculator"))
            .to(LooseItemMovImpl::class.java)
        bind(Mov::class.java)
            .annotatedWith(Names.named("bulk_item_mov_calculator"))
            .to(BulkItemMovImpl::class.java)
        bind(PacmanMovCalculator::class.java).to(PacmanMovCalculatorImpl::class.java)
        bind(MaxUnit::class.java).to(MaxUnitImpl::class.java)
        bind(Moq::class.java).to(MoqImpl::class.java)
        bind(ContextFactory::class.java).to(ContextFactoryImpl::class.java)
        bind(BusinessValidator::class.java).to(BusinessValidatorImpl::class.java)
        bind(OrderFormEventTracker::class.java).to(OrderFormEventTrackerImpl::class.java)
        bind(InventoryProvider::class.java).to(InventoryProviderImpl::class.java)
        bind(CatalogProvider::class.java).to(CatalogProviderImpl::class.java)
        bind(CatalogProvider::class.java)
            .annotatedWith(Names.named("cached_listing_provider"))
            .to(CachedCatalogProviderImpl::class.java)
        bind(OrgProvider::class.java).to(OrgProviderImpl::class.java)
        bind(PriceProvider::class.java).to(PriceProviderImpl::class.java)
        bind(PriceContextInterpreter::class.java).to(PriceContextInterpreterImpl::class.java)
        bind(PriceContextInterpreter::class.java)
            .annotatedWith(Names.named("lot_based_price_interpreter"))
            .to(LotBasedPriceInterpreter::class.java)
        bind(PriceContextInterpreter::class.java)
            .annotatedWith(Names.named("lot_scheme_price_interpreter"))
            .to(LotSchemePriceInterpreter::class.java)
        bind(LogisticsProvider::class.java).to(LogisticsProviderImpl::class.java)
        bind(InventoryReservationProvider::class.java).to(InventoryReservationProviderImpl::class.java)
        bind(ReservationInfoDao::class.java).to(ReservationInfoDaoImpl::class.java)
        bind(ReservationInfoRepo::class.java).to(ReservationInfoRepoImpl::class.java)
        bind(ReservationInfoBulkReadRepo::class.java).to(ReservationInfoBulkReadRepoImpl::class.java)
        bind(TaxProvider::class.java).to(TaxProviderImpl::class.java)
        bind(LevyProvider::class.java).to(LevyProviderImpl::class.java)
        bind(PromoManager::class.java).to(PromoManagerImpl::class.java)
        bind(ReservationManager::class.java).to(ReservationManagerImpl::class.java)
        bind(RewardProvider::class.java).to(RewardProviderImpl::class.java)
        bind(OrderFormReadRepo::class.java).to(DefaultOrderFormReadRepo::class.java)
        bind(OrderFormWriteRepo::class.java).to(DefaultOrderFormWriteRepo::class.java)
        bind(ExceptionHandlerFactory::class.java).to(DefaultExceptionHandlerFactory::class.java)
        bind(InventoryProvider::class.java).to(InventoryProviderImpl::class.java)
        bind(DeliverySlotProvider::class.java).to(DeliverySlotProviderImpl::class.java)
        bind(LogChargeProvider::class.java).to(LogChargeProviderImpl::class.java)
        bind(PreOrderEtaProvider::class.java).to(PreOrderEtaProviderImpl::class.java)
        bind(BuyerHubProvider::class.java).to(BuyerHubProviderImpl::class.java)
        bind(OrgIdentityProvider::class.java).to(OrgIdentityProviderImpl::class.java)
        bind(ResponseBuilder::class.java)
            .annotatedWith(Names.named("default_response_builder"))
            .to(DefaultResponseBuilder::class.java)
        bind(ResponseBuilder::class.java)
            .annotatedWith(Names.named("default_multi_response_builder"))
            .to(DefaultMultipleOFResponseBuilder::class.java)
        bind(ResponseBuilder::class.java)
            .annotatedWith(Names.named("pacman_response_builder"))
            .to(PacmanResponseBuilder::class.java)
        bind(OrderFormSelector::class.java)
            .annotatedWith(Names.named("default_of_selector"))
            .to(DefaultOrderFormSelector::class.java)
        bind(OrderFormSelector::class.java)
            .annotatedWith(Names.named("horeca_of_selector"))
            .to(HorecaOrderFormSelector::class.java)
        bind(TransactionLifecycle::class.java)
            .annotatedWith(Names.named("default_tx_lifecycle"))
            .to(DefaultTransactionLifecycle::class.java)
        bind(object : TypeLiteral<AbstractCreateOrderLifecycleV2<CreateOrderFormReqV2Dto>>() {})
            .annotatedWith(Names.named("default_create_order_v2"))
            .to(DefaultCreateOrderLifecycleImpl::class.java)
        bind(object : TypeLiteral<AbstractEditOrderLifecycleV2<EditOrderFormReqV2Dto>>() {})
            .annotatedWith(Names.named("default_edit_order_v2"))
            .to(DefaultEditOrderLifecycleImpl::class.java)
        bind(object : TypeLiteral<AbstractDeleteOrderLifecycleV2<DeleteOrderFormReqV2Dto>>() {})
            .annotatedWith(Names.named("default_delete_order_v2"))
            .to(DefaultDeleteOrderLifecycleImpl::class.java)
        bind(object : TypeLiteral<AbstractFetchOrderLifecycleV2<FetchOrderFormReqV2Dto>>() {})
            .annotatedWith(Names.named("default_order_fetch_v2"))
            .to(DefaultFetchOrderLifecycleImpl::class.java)
        bind(object : TypeLiteral<AbstractReserveLifecycleV2<ReserveRequestV2Dto>>() {})
            .annotatedWith(Names.named("default_reserve_v2"))
            .to(DefaultReserveLifecycleImpl::class.java)
        bind(object : TypeLiteral<AbstractPlaceOrderLifecycleV2<ConfirmRequestV2Dto>>() {})
            .annotatedWith(Names.named("default_order_place_v2"))
            .to(DefaultPlaceOrderLifecycleImpl::class.java)
        bind(object : TypeLiteral<OrderFormService<BaseRequestDto, BaseResponseDto>>() {})
            .annotatedWith(Names.named("default_of_service"))
            .to(DefaultOrderFormService::class.java)
        bind(object : TypeLiteral<OrderFormService<BaseRequestDto, BaseResponseDto>>() {})
            .annotatedWith(Names.named("pacman_of_service"))
            .to(PacmanOrderFormService::class.java)
        bind(object : TypeLiteral<OrderFormService<BaseRequestDto, BaseResponseDto>>() {})
            .annotatedWith(Names.named("pharma_of_service"))
            .to(PharmaOrderFormService::class.java)
        bind(CheckoutResponseBuilder::class.java)
            .annotatedWith(Names.named("default_checkout_response_builder"))
            .to(DefaultCheckoutResponseBuilder::class.java)
        bind(object : TypeLiteral<CheckoutService<BaseRequestDto, BaseResponseDto>>() {})
            .annotatedWith(Names.named("default_checkout_service"))
            .to(DefaultCheckoutService::class.java)
        bind(object : TypeLiteral<AbstractCheckoutLifecycleV2<CheckoutReqDto>>() {})
            .annotatedWith(Names.named("default_checkout_lifecycle"))
            .to(DefaultCheckoutLifecycleImpl::class.java)
    }

    @Singleton
    @Provides
    fun redisLettuceClientPool(): RedisLettuce6Client {
        return ResourceBuilder.redisClient("order-mgmt").buildLettuce6Client()
    }

    @Singleton
    @Provides
    @Named("user-metrics")
    fun getUserMetricsRedis(): RedisLettuce6Client {
        return ResourceBuilder.redisClient("user-metrics").buildLettuce6Client()
    }

    @Singleton
    @Provides
    @Named("order_event")
    fun eventHubClient(@Named("executor-service") executors: ScheduledExecutorService): EventHubClient {
        return ResourceBuilder.eventHubClient(EVENT_HUB_ORDER_RESOURCE_ID, executors)
    }

    @Singleton
    @Provides
    @Named("order_delta_event")
    fun orderDeltaEventHubClient(@Named("executor-service") executors: ScheduledExecutorService): EventHubClient {
        return ResourceBuilder.eventHubClient(EVENT_HUB_ORDER_DELTA_RESOURCE_ID, executors)
    }

    /**
     * Extra Event Hub Client to add more consumer groups
     */
    @Singleton
    @Provides
    @Named("order_delta_event_v2")
    fun orderDeltaEventV2HubClient(@Named("executor-service") scheduledExecutorService: ScheduledExecutorService): EventHubClient {
        return ResourceBuilder.eventHubClient(EVENT_HUB_ORDER_DELTA_V2_RESOURCE_ID, scheduledExecutorService)
    }

    @Singleton
    @Provides
    private fun providedUserServiceClient(): UserServiceClient {
        val userServiceClient = UserServiceClient(getUserClientConfig())
        closeableListener.accept(userServiceClient)
        return userServiceClient
    }

    @Singleton
    @Provides
    private fun providedPricingServiceClient(): PricingClient {
        val pricingClient = PricingClient(getPricingClientConfig())
        closeableListener.accept(pricingClient)
        return pricingClient
    }

    @Singleton
    @Provides
    private fun providedLotPricingServiceClient(): LotPricingClient {
        val lotPricingClient = LotPricingClient(getPricingClientConfig())
        closeableListener.accept(lotPricingClient)
        return lotPricingClient
    }

    @Singleton
    @Provides
    fun getPaymentsServiceClient(): PaymentServiceClient {
        return PaymentServiceClient(getPaymentsServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getFeatureStoreClient(): FeatureStoreClient {
        return FeatureStoreClient(getFeatureStoreClientConfig())
    }

    @Singleton
    @Provides
    fun getPromotionsServiceClient(): PromotionsServiceClient {
        return PromotionsServiceClient(getPromotionsClientConfig())
    }

    @Singleton
    @Provides
    fun getWarehouseClientV2(): WarehouseClientV2 {
        return WarehouseClientV2(getWarehouseServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getInventoryServiceClient(): InventoryServiceClient {
        return InventoryServiceClient(getInventoryServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getInventoryReservationServiceClient(): InventoryReservationClient {
        return InventoryReservationClient(getInventoryReservationServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getLogisticsServiceClient(): LogisticsServiceClient {
        return LogisticsServiceClient(getLogisticsConsoleServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getLogisticsConsoleServiceClient(): LogisticsConsoleServiceClient {
        return LogisticsConsoleServiceClient(getLogisticsConsoleServiceClientConfig())
    }

    @Singleton
    @Provides
    fun geFPJITServiceabilityClient(): LogisticsFPJITServiceabilityClient {
        return LogisticsFPJITServiceabilityClient(getLogisticsConsoleServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getNotificationClient(): NotificationClient {
        return NotificationClient(getNotificationServiceClientConfig())
    }

    @Singleton
    @Provides
    private fun chatClient(): ChatServiceClient {
        val serviceClient = ChatServiceClient(getChatClientConfig())
        closeableListener.accept(serviceClient)
        return serviceClient
    }

    @Singleton
    @Provides
    private fun orgServiceClient(): OrgServiceClient = OrgServiceClient(getOrgClientConfig()).apply {
        closeableListener.accept(this)
    }

    @Singleton
    @Provides
    fun getOrgInternalIdentityCacheClient(orgServiceClient: OrgServiceClient) =
        OrgInternalIdentityCacheClient(orgServiceClient, cacheMetrics)

    @Singleton
    @Provides
    private fun orgRepository() = RedisOrgRepository(
        getOrgClientConfig(),
        RedisOrgRepository.defaultLocalCacheConfig,
        RedisOrgRepository.defaultLocalCacheConfig.copy(size = 500)
    )

    @Singleton
    @Provides
    private fun provideCatalogClient(): CatalogServiceClient = CatalogServiceClient(getCatalogClientConfig()).apply {
        closeableListener.accept(this)
    }

    @Singleton
    @Provides
    private fun provideMaxQuantityClient(): MaxQuantityClient = makeClient("orchestrator-service")

    @Singleton
    @Provides
    private fun provideListingTaggingClient(): ListingTaggingClient = makeClient("listing-tags-service")

    @Singleton
    @Provides
    private fun provideListingRedisRepositoryClient(): RedisListingRepository {
        return RedisListingRepository(getRedisListingRepositoryConfig())
    }

    @Singleton
    @Provides
    private fun provideInvoicingClient(): InvoicingServiceClient =
        InvoicingServiceClient(getInvoicingClientConfig()).apply {
            closeableListener.accept(this)
        }

    @Singleton
    @Provides
    internal fun getStorageServiceClient(): StorageServiceClient {
        return StorageServiceClient("order-mgt", getStorageServiceClientConfig())
    }

    private fun getClientConfig(configKey: String): UdaanClientConfig {
        return UdaanServerConfig[configKey]!!
    }

    private inline fun <reified T : UdaanServiceClient> makeClient(configKey: String): T {
        val config: UdaanClientConfig = getClientConfig(configKey)
        return T::class.java.getDeclaredConstructor(UdaanClientConfig::class.java).newInstance(config)
    }

    @Singleton
    @Provides
    @Named("executor-service")
    fun providerExecutorService() = Executors.newScheduledThreadPool(128)

    private fun protoJsonPrinter(): JsonFormat.Printer {
        return JsonFormat.printer()
            .preservingProtoFieldNames()
            .includingDefaultValueFields()
            .omittingInsignificantWhitespace()
    }

    private fun protoJsonParser(): JsonFormat.Parser {
        return JsonFormat.parser().ignoringUnknownFields()
    }

    @Singleton
    @Provides
    internal fun getPricingOptionsClientV2(
        chatServiceClient: ChatServiceClient,
        creditRepository: CreditRepository,
        pharmaPricingOption: PharmaPricingOption
    ): PricingOptionsClientV2 {
        return PricingOptionsClientV2(
            chatServiceClient,
            creditRepository,
            getCreditServiceClientConfig(),
            pharmaPricingOption
        )
    }

    @Singleton
    @Provides
    fun getCreditServiceClient(): CreditServiceClient {
        return CreditServiceClient(getCreditServiceClientConfig())
    }

    @Singleton
    @Provides
    fun getProductMasterServiceClient(): ProductMasterServiceClient {
        return ProductMasterServiceClient(getFulfilmentCatalogClientConfig())
    }

    @Singleton
    @Provides
    fun getRedisProductMasterRepository(): RedisProductMasterRepository {
        return RedisProductMasterRepository(getFulfilmentCatalogClientConfig())
    }

    @Singleton
    @Provides
    fun getFulfilmentOrchestratorServiceClient(): FulfilmentOrchestrationServiceClient {
        return FulfilmentOrchestrationServiceClient(getFulfilmentClientConfig())
    }

    @Singleton
    @Provides
    fun getOrchServingTenantsClient(): ServingTenantsClient {
        return ServingTenantsClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getATPOrchestrationClient(): ATPOrchestrationClient {
        return ATPOrchestrationClient(getFulfilmentClientConfig())
    }

    @Singleton
    @Provides
    fun getOrchestratorPromiseServiceClient(): PromiseServiceClient {
        return PromiseServiceClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getOrchAvlServiceClient(): AvailabilityServiceClient {
        return AvailabilityServiceClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getBuyerHubMappingClient(): BuyerHubMappingCacheClient {
        return BuyerHubMappingCacheClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getBusinessConfigClient(): BusinessConfigClient {
        return ConfigClientFactory.getBusinessConfigClientV2(
            config = getClientConfig("config-service"),
            moduleName = "orderform-service"
        )
    }

    @Singleton
    @Provides
    @Named(DIConstants.BusinessConfigClient.ORCHESTRATOR)
    fun getOrchBusinessConfigClient(): BusinessConfigClient {
        return ConfigClientFactory.getBusinessConfigClientV2(
            config = getClientConfig("config-service"),
            moduleName = "orchestrator-service"
        )
    }

    @Singleton
    @Provides
    @Named(DIConstants.BusinessConfigClient.LOGISTICS)
    fun getLogisticsBusinessConfigClient(): BusinessConfigClient {
        return ConfigClientFactory.getBusinessConfigClientV2(
            config = getClientConfig("config-service"),
            moduleName = "logistics-service"
        )
    }

    @Singleton
    @Provides
    fun getCreditRepository(): CreditRepository {
        return CreditRepository(
            creditServiceConfig = getCreditServiceClientConfig(),
            creditFixedInfoCacheSize = 5000, creditFixedInfoCacheExpiryMinutes = 30,
            orgFilterTagsSize = 5000, orgFilterTagsCacheExpiryMinutes = 30,
            creditPolicyCacheSize = 20, creditPolicyCacheExpiryMinutes = TimeUnit.DAYS.toMinutes(10)
        )
    }

    @Singleton
    @Provides
    fun getDeliverySlotsServiceClient(): DeliverySlotsServiceClient {
        return DeliverySlotsServiceClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getReservationServiceClient(): ReservationServiceClient {
        return ReservationServiceClient(getReservationClientConfig())
    }

    @Singleton
    @Provides
    fun getServiceabilityClient(): ServiceabilityClient {
        return ServiceabilityClient(getOrchestratorClientConfig())
    }

    @Singleton
    @Provides
    fun getMangedFFServiceClient(): ManagedFulfillmentServiceClient {
        return ManagedFulfillmentServiceClient(getFulfilmentClientConfig())
    }

    @Singleton
    @Provides
    fun getBuyerProfileClient(): BuyerProfileClient {
        return BuyerProfileClient()
    }

    @Named("tradequality")
    @Provides
    @Singleton
    fun getBuyerProfileClientTQ(): com.udaan.tradequality.client.BuyerProfileClient {
        return com.udaan.tradequality.client.BuyerProfileClient(getTradeQualityConfig())
    }

    @Singleton
    @Provides
    fun getRewardsClient(): RewardsClient {
        return RewardsClient(getRewardsServiceConfig())
    }

    @Singleton
    @Provides
    fun getTradeQualityClient(): TradeQualityBuyerRiskClient {
        return TradeQualityBuyerRiskClient(getTradeQualityConfig())
    }

    @Singleton
    @Provides
    fun getRiskClient(): RiskProfileClient {
        return RiskProfileClient(getTradeQualityConfig())
    }

    @Singleton
    @Provides
    fun getOrderProfilingClient(): OrderProfilingClient {
        return OrderProfilingClient(getTradeQualityConfig())
    }

    @Singleton
    @Provides
    fun getComboClient(): ComboClient {
        return ComboClient(getCatalogClientConfig())
    }

    @Singleton
    @Provides
    fun getVerticalCache(catalogServiceClient: CatalogServiceClient): VerticalCache {
        return VerticalCache(catalogServiceClient)
    }

    @Singleton
    @Provides
    fun getMarioDistributionClient(): MarioDistributionClient {
        return MarioDistributionClient(getMarioDistributionClientConfig())
    }

    @Singleton
    @Provides
    fun getCatalogComboClientV2(): CatalogCombosClientV2 {
        return CatalogCombosClientV2(getCatalogClientConfig())
    }

    /**
     * NOTE: CategoryTreeV2 initialises complete tree in init with a sync call, this can slow down app start
     */
    @Singleton
    @Provides
    private fun getCategoryTreeV2(catalogServiceClient: CatalogServiceClient) = CategoryTreeV2(catalogServiceClient)

    @Singleton
    @Provides
    private fun getCategoryGroupHelper(
        verticalCache: VerticalCache,
        orgRepository: RedisOrgRepository,
        redisListingRepository: RedisListingRepository,
        categoryTreeV2: CategoryTreeV2
    ) = CategoryConfigHelper(
        verticalCache,
        orgRepository,
        redisListingRepository,
        categoryTreeV2
    )

    @Singleton
    @Provides
    private fun getComplianceServiceClient(): ComplianceServiceClient {
        return ComplianceServiceClient(getComplianceServiceClientConfig())
    }

    @Singleton
    @Provides
    private fun getSubscriptionBuyerClient(): SubscriptionBuyerClient {
        return SubscriptionBuyerClient()
    }

    @Singleton
    @Provides
    private fun getLogisticsVSLClient(): LogisticsVSLClient {
        return LogisticsVSLClient(getLogisticsConsoleServiceClientConfig())
    }

    private val idAdvanceBlockStore: IdBlockAdvanceStore by lazy {
        SqlServerBlockAdvanceStore(ResourceBuilder.jdbi3("order-form").build())
    }

    @Provides
    @Singleton
    @Named("orderIdGen")
    private fun orderIdGenerator() = IdentityGenerator("OD", idAdvanceBlockStore)

    @Provides
    @Singleton
    @Named("mainOrderIdGen")
    private fun mainOrderIdGenerator() = IdentityGenerator("MOD", idAdvanceBlockStore)

    @Singleton
    @Provides
    @Named("search-redis")
    private fun searchRedis() =
        ResourceBuilder.redisClient("search/cache").buildLettuce6Client()

    @Singleton
    @Provides
    private fun getOrderServiceClient(): SellerOrderServiceClient =
        makeClient("order-mgt-service")

    @Singleton
    @Provides
    private fun getDrugLicenseClient(): DrugLicenseClient =
        DrugLicenseClient(getUserClientConfig())

    @Singleton
    @Provides
    fun getTradeCreditServiceClient(): TradeCreditClient {
        return TradeCreditClient(getTradeCreditClientConfig())
    }
}

const val EVENT_HUB_ORDER_RESOURCE_ID = "order-mgmt/order"
const val EVENT_HUB_ORDER_DELTA_RESOURCE_ID = "order-mgmt/order-delta"
const val EVENT_HUB_ORDER_DELTA_V2_RESOURCE_ID = "order-mgmt/order-delta-v2"
const val EVENT_HUB_ORDER_DELTA_BUYER_GMV_TRACKER_CONSUMER = "buyer-gmv-tracker/order-delta"
