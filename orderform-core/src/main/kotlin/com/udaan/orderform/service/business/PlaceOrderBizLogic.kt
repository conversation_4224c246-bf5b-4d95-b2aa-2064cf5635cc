package com.udaan.orderform.service.business

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.google.protobuf.Value
import com.udaan.cart.common.LogisticsProvider
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.client.combos.CatalogCombosClientV2
import com.udaan.catalog.client.helpers.Category
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.models.SellerOrderInternals
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.order_mgt.models.states.StatesV1
import org.jdbi.v3.core.Handle
import java.util.concurrent.CompletableFuture
import com.udaan.orderform.dal.dao.SellerOrderDao
import com.udaan.proto.representations.OrgV1
import com.udaan.user.client.OrgServiceClient
import com.udaan.user.client.RedisOrgRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import com.udaan.orderform.service.utils.CatalogBatchCall
import java.util.*
import com.udaan.catalog.models.ModelV2
import com.udaan.common.server.getSync
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.paiseToRsStr
import com.udaan.common.utils.parallelMap
import com.udaan.config.Configuration
import com.udaan.config.client.BusinessConfigClient
import com.udaan.fulfilment.common.utils.executeAwait
import com.udaan.id.IdentityGenerator
import com.udaan.instrumentation.Telemetry
import com.udaan.inventory.api.v2.reservation.PromisedInventoryTierType
import com.udaan.invoicing.client.InvoicingServiceClient
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.logistics.client.LogisticsConsoleServiceClient
import com.udaan.logistics.models.sla.mario.DeliveryEstimateBulkRequest
import com.udaan.logistics.models.sla.mario.DeliveryEstimateRequest
import com.udaan.logistics.models.sla.mario.DeliveryEstimateResponse
import com.udaan.model.ExemptStatus
import com.udaan.model.gstin
import com.udaan.om.dal.LaneSla
import com.udaan.om.dal.SellerOrderLineDao
import com.udaan.orchestrator.client.PromiseServiceClient
import com.udaan.orchestrator.models.SlaPromiseRequest
import com.udaan.order_mgt.client.LogisticsServiceClient
import com.udaan.order_mgt.events.model.PromiseInfoResponse
import com.udaan.order_mgt.models.OrderLineLevy
import com.udaan.order_mgt.models.ShippingV1
import com.udaan.order_mgt.representations.OrderV2
import com.udaan.order_mgt.representations.logicstics_view.LogisticsViewV1
import com.udaan.orderform.dal.repository.OrderFormDAL
import com.udaan.orderform.dal.repository.RouteDAL
import com.udaan.orderform.service.business.logistics.selection.ShippingProfileSelectorWithConn
import com.udaan.orderform.service.business.risk.PolicyBasedRiskDetector
import com.udaan.logistics.models.vsl.ShippingChargeProfile
import com.udaan.orchestrator.client.DeliverySlotsServiceClient
import com.udaan.orchestrator.models.GetAppliedDeliverySlotRequest
import com.udaan.orchestrator.models.deliverySlots.ValidateDeliverySlotsRequest
import com.udaan.orchestrator.models.deliverySlots.ValidateDeliverySlotsRequestItem
import com.udaan.orderform.cart.common.getQualifierTags
import com.udaan.orderform.cart.common.models.*
import com.udaan.orderform.common.providers.contexts.PriceContext
import com.udaan.orderform.cart.common.toOMSOrderLineLevies
import com.udaan.orderform.cart.domain.OrderDeliveryChargeFactory
import com.udaan.orderform.cart.domain.OrderLineDeliveryChargeFactory
import com.udaan.orderform.common.providers.CatalogProvider
import com.udaan.orderform.common.executeAwaitWithEachTimeOutAndRetries
import com.udaan.orderform.common.providers.ListingSalesUnitRef
import com.udaan.orderform.common.providers.PriceProvider
import com.udaan.orderform.common.providers.PriceRequest
import com.udaan.orderform.constants.BusinessConfigKey
import com.udaan.orderform.service.exceptions.*
import com.udaan.orderform.service.payments.OrderPaymentHandler
import com.udaan.orderform.service.telemetry.*
import com.udaan.orderform.fulfillment.OrchestratorUtils
import com.udaan.orderform.models.*
import com.udaan.orderform.service.business.risk.PrepaymentData
import com.udaan.orderform.service.utils.derivePlatform
import com.udaan.payments.api.CreateGroupPrepaymentRequest
import com.udaan.payments.api.PrepaymentPurposeType
import com.udaan.payments.api.PrepaymentRequestDTO
import com.udaan.payments.client.PaymentServiceClient
import com.udaan.pricing.PriceForListing
import com.udaan.pricing_options.core.PricingOptionsClientV2
import com.udaan.pricing_options.model.PricingOptionsRequest
import com.udaan.proto.models.ModelV1.SellingPlatform
import com.udaan.proto.tax.models.TaxV1
import com.udaan.resources.RedisLettuce6Client
import com.udaan.tradequality.client.OrderProfilingClient
import com.udaan.tradequality.models.riskProfiling.OrderMetaData
import com.udaan.tradequality.models.riskProfiling.OrderRiskV2Request
import org.jdbi.v3.core.Jdbi
import java.text.SimpleDateFormat
import kotlin.math.min
import kotlin.math.roundToLong
import org.jdbi.v3.core.kotlin.inTransactionUnchecked

@Singleton
class PlaceOrderBizLogic @Inject constructor(
    private val invoicingServiceClient: InvoicingServiceClient,
    private val catalogCombosClient: CatalogCombosClientV2,
    private val logisticsServiceClient: LogisticsServiceClient,
    private val riskDetector: PolicyBasedRiskDetector,
    private val fulfillmentBizLogicV2: FulfillmentBizLogicV2,
    private val orgServiceClient: OrgServiceClient,
    private val orgRepository: RedisOrgRepository,
    private val taxationBizLogic: TaxationBizLogicV2,
    private val pricingOptionsClient: PricingOptionsClientV2,
    private val promiseServiceClient: PromiseServiceClient,
    private val deliverySlotsServiceClient: DeliverySlotsServiceClient,
    private val shippingProfileSelector: ShippingProfileSelectorWithConn,
    private val orderFormDAL: OrderFormDAL,
    private val routeDal: RouteDAL,
    private val redisClientPool: RedisLettuce6Client,
    private val orderPaymentHandler: OrderPaymentHandler,
    private val pricingService: PricingService,
    private val orderProfilingClient: OrderProfilingClient,
    private val categoryConfigHelper: CategoryConfigHelper,
    private val logisticsProvider: LogisticsProvider,
    private val priceProvider: PriceProvider,
    private val catalogProvider: CatalogProvider,
    private val businessConfigClient: BusinessConfigClient,
    private val logisticsConsoleServiceClient: LogisticsConsoleServiceClient,
    private val paymentServiceClient: PaymentServiceClient,
    private val objectMapper: ObjectMapper,
    @Named("mainOrderIdGen") private val mainOrderIdGenerator: IdentityGenerator,
    private val dbi: Jdbi
) {
    companion object {
        private val logger by logger()
        private val df = SimpleDateFormat("MMM d, yyyy")
        val enableSellerMessageOnOrderNotes = Configuration.getBoolean("seller_message_on_order_note", false)!!
        private const val priceEvent = "ORDER_FORM_LOT_REQUEST"
        private const val MAX_COD_LIMIT = 199999 * 100L
        private const val PHARMA_OFFER_INV_EXP_KEY = "pharma-offer-inv-exp-cities"
        private const val COD_LIMIT_EXEMPTIONS_KEY = "cod-limit-exemptions"
    }

    private fun isEnterpriseBuyer(buyerOrg: com.udaan.proto.models.ModelV1.OrgAccount) =
            buyerOrg.data.accountMarkersList.contains(com.udaan.proto.models.ModelV1.OrgAccountData.AccountMarker.ENTERPRISE)

    private fun isInterCitySeller(sellerOrg: OrgV1.OrgAccountExtendedResponse) =
            isIntraCitySeller(sellerOrg).not()

    private fun isIntraCitySeller(sellerOrg: OrgV1.OrgAccountExtendedResponse) =
            Category.isAnyCategoryIntraCity(sellerOrg.sellingConditions.sellingCategoriesList)

    private suspend fun validateSellerAvailability(sellerOrg: OrgV1.OrgAccountExtendedResponse) = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        sellerOrg.sellerOooSettings?.let { timeRange ->
            if (timeRange.contains(currentTime)) {
                throw SellerOOOException("Seller is temporarily closed, please check after ${df.format(Date(timeRange.last))}")
            }
        }
    }

    private suspend fun checkPaymentLimit(
        paymentMode: PaymentGatewayV1.PaymentInstrument,
        orderValue: Long,
        buyerId: String
    ): Long {
        return if (paymentMode == PaymentGatewayV1.PaymentInstrument.COD) {
            val codLimitForBuyer = getCodLimit(buyerId)
            if (orderValue > codLimitForBuyer)
                codLimitForBuyer
            else
                0L
        } else 0L
    }

    private fun validateBlockedSeller(sellerOrg: OrgV1.OrgAccountExtendedResponse, isManaged: Boolean = false) {
        val sellerAvailabilitySpecs = logisticsServiceClient.getSellerAvailabilitySpecs(sellerOrg.orgAccount.orgId).executeSync()
        if (sellerAvailabilitySpecs.marketPlaceAvailable.not() && isManaged.not()) {
            throw RouteUnavailableException("Pickups from seller are temporarily blocked")
        }
        if (sellerAvailabilitySpecs.warehouseAvailable.not() && isManaged) {
            throw InventoryUnavailableException("Listing is temporarily not available")
        }
    }

    private fun validateBlockedServiceability(buyerOrg: com.udaan.proto.models.ModelV1.OrgAccount, buyerOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit, sellerOrg: OrgV1.OrgAccountExtendedResponse, categoryTree: LogisticsViewV1.CategoryTree): CompletableFuture<Unit> {
        return TelemetryScope.future(Dispatchers.IO) {
            val buyerServiceabilitySpecs = logisticsServiceClient.isBuyerServiceabilityBlockedV2(
                    buyerOrg.orgId,
                    buyerOrgUnit.orgUnitId,
                    categoryTree.categoryGroup,
                    sellerOrg.orgAccount.orgId,
                    categoryTree.vertical).executeSync()
            if (buyerServiceabilitySpecs.serviceabilityBlocked) {
                throw RouteUnavailableException(buyerServiceabilitySpecs.statusCode.displayMessage)
            }
        }
    }

    private suspend fun checkForServiceProviders(
        orderId: String,
        vsl: ShippingV1.LogisticsRouteVslMaster,
        paymentInstrument: List<PaymentGatewayV1.PaymentInstrument>,
        shippingProfile: ShippingChargeProfile,
        buyerOrgUnitId: String,
        sellerOrgUnitId: String,
        buyerPincode: String
    ): Triple<Boolean, LogisticsServiceDeniedReason?, String?> {
        val logisticService = getLogisticsServiceType(paymentInstrument.map { ModelV1.OrderPayments.newBuilder().apply { this.paymentMethod = it }.build() })
        val providers = logisticsProvider.findProviderDetails(vsl, logisticService)
        val filteredProviders = shippingProfile.strictFilter(providers.map { it.provider })
        return if (filteredProviders.isNotEmpty()) {
            Triple(true, null, null)
        } else {
            logger.error("No profile providers found for $orderId ${vsl.srcPincode} - ${vsl.destPincode} , ${logisticService.name} for shippingProfile ${shippingProfile.name}")
            val (errorCode, errorMessage) = when (shippingProfile) {
                ShippingChargeProfile.HOME_AND_KITCHEN,
                ShippingChargeProfile.FOOTWEAR,
                ShippingChargeProfile.LARGE,
                ShippingChargeProfile.CLOTHING,
                ShippingChargeProfile.ELECTRONICS,
                ShippingChargeProfile.DEVICES ->
                    if(logisticsProvider.isOrgUnitLmBypassed(buyerOrgUnitId, sellerOrgUnitId, buyerPincode)) {
                        return Triple(true, null, null)
                    } else {
                        LogisticsServiceDeniedReason.PINCODE_UNSERVICEABLE to "Delivery services are not available for items in your order-form for (${vsl.srcPincode} -> ${vsl.destPincode})"
                    }
                ShippingChargeProfile.PHARMA -> LogisticsServiceDeniedReason.PINCODE_UNSERVICEABLE to "Delivery services are not available for (${vsl.srcPincode} -> ${vsl.destPincode}) - [${logisticService.name}]"
                else -> LogisticsServiceDeniedReason.VENDOR_NOT_FOUND to "Courier provider not found for (${vsl.srcPincode} -> ${vsl.destPincode})."
            }

            Telemetry.trackEvent(EVENT_EX_ORDER_LOGISTICS_NOT_SUPPORTED,
                    mapOf(
                            DIM_FF_ORDER_PLACEMENT_ORDER_ID to orderId,
                            DIM_FF_ORDER_PLACEMENT_ORDER_EX to errorMessage,
                            DIM_SRC_PICKUP_CLUSTER to vsl.pickUpCluster,
                            DIM_DEST_PINCODE to vsl.destPincode,
                            DIM_SHIPPING_PROFILE to shippingProfile.name,
                            DIM_LOGISTICS_SERVICE to logisticService.name
                    ),
                    mapOf())
            Triple(false, errorCode, errorMessage)
        }
    }

    private data class PharmaAttributes(val cashDiscountBps: Double, val scheme: String)

	private suspend fun getPharmaAttributes(
        shippingProfile: ShippingChargeProfile,
        orderLine: ModelV1.OrderLine,
        listing: ModelV2.TradeListing?,
        comboDetails: Map<String, List<ComboDetails>>,
        priceContext: PriceContext?,
        buyerOrg: com.udaan.proto.models.ModelV1.OrgAccount,
        toOrgUnitId: String,
	): PharmaAttributes {
        fun getSpecAttribute(verticalSpecs: Map<String, Value>, attributeName: String): String? {
            var valueInKey = verticalSpecs[attributeName]?.listValue?.valuesList?.firstOrNull()?.stringValue
            if (valueInKey.isNullOrEmpty()) {
                valueInKey = verticalSpecs[attributeName]?.stringValue
            }
            return valueInKey
        }

        return if (listing == null || shippingProfile != ShippingChargeProfile.PHARMA) PharmaAttributes(0.0, "")
        else {
            val promoDiscounts = orderLine.properties.promotionDataList.sumBy {
                it.discountBps
            }
            // get the updated cashdiscount after applying any buyer specific additional discounts
			val comboPricingInfo = if (orderLine.isCombo()) {
				comboDetails[orderLine.properties.comboId]?.find { it.salesUnitId == orderLine.salesUnitId }?.pricingInfo
			} else null
            val listingWithPricingOption = pricingOptionsClient.getPricingOptionsV2(
                PricingOptionsRequest(
                    listing,
                    buyerOrg.orgId,
                    comboPricingInfo = comboPricingInfo,
                    priceForListing = priceContext?.getListingPrice(
                        orderLine.listingId,
                        orderLine.salesUnitId,
                        orderLine.miscInfo.ffSkuId
                    )
                )
            ).tradeListing
            val verticalSpecs = listingWithPricingOption.verticalSpec?.fieldsMap
            val pharmaAttributes = if (verticalSpecs == null || verticalSpecs.isEmpty()) {
                PharmaAttributes(0.0, "")
            } else {
                val cashDiscount = getSpecAttribute(verticalSpecs, "mes_cash_discount")
                logger.info(
                    "[mes_cash_discount]: " +
                            "${orderLine.sellerOrderId} - ${orderLine.orderLineId} - existing pharma discount " +
                            "${orderLine.offerDetails.pharmaBuyerDiscountBps} - ${orderLine.offerDetails.description}" +
                            "new pharma discount $cashDiscount - ${
                                getSpecAttribute(
                                    verticalSpecs,
                                    "mes_discount_reason"
                                )
                            } - ${getSpecAttribute(verticalSpecs, "pha_lotid")}"
                )
                try {
                    if (cashDiscount?.toInt() != orderLine.offerDetails.discountBps) {
                        logger.info("[Pharma discount mismatch] " +
                                "${orderLine.sellerOrderId} - ${orderLine.orderLineId} is not matching")
                    }
                } catch (e: Exception) {
                    logger.error("[mes_discount_reason] Int parsing error: ${e.message}", e)
                }
                val scheme = getSpecAttribute(verticalSpecs, "mes_scheme") ?: ""
                logger.info("[Pharma Attributes] " +
                        "Scheme ${orderLine.sellerOrderId} - ${orderLine.orderLineId} - $scheme")
                val cashDiscountBps = cashDiscount?.toDoubleOrNull()?.times(100) ?: 0.0
                val pharmaInvExp = buyerOrg.orgUnitsMap[toOrgUnitId]?.unitAddress?.city?.let { city ->
                    kotlin.runCatching {
                        businessConfigClient.sIsMember(PHARMA_OFFER_INV_EXP_KEY, city)
                    }.getOrElse { false }
                } ?: false
                val discountBps = if (pharmaInvExp) {
                    val orderLineAmount = orderLine.perUnitSpPaise
                    val originalAmount = orderLine.offerDetails.originalPricePaisa
                    val bps = (originalAmount.toDouble() - orderLineAmount.toDouble())
                        .div(originalAmount)
                        .times(10000.0)
                        .roundToLong()
                    logger.info(
                        "[PharmaInvExp] Enabled exp for ${orderLine.sellerOrderId}, " +
                                "buyer id: ${buyerOrg.orgId}, bps: $bps"
                    )
                    bps.toDouble()
                } else {
                    cashDiscountBps + promoDiscounts
                }
                logger.info("Pharma ${orderLine.sellerOrderId} discount bps: $discountBps")
                PharmaAttributes(discountBps, scheme)
            }
            logger.info("[Pharma Attributes] " +
                    "For ${orderLine.sellerOrderId} - ${orderLine.orderLineId} - $pharmaAttributes")
            pharmaAttributes
        }
    }

    private fun isPrepaidOnlyOrder(listingMap: Map<String, ModelV2.TradeListing>) =
            listingMap.any { it.value.config.prepaidOnly }

    private fun unitPricePaise(salesUnit: ModelV2.SalesUnit, qty: Int, priceForListing: PriceForListing?): Long {
        return if (pricingService.enabled(salesUnit.listingId) && salesUnit.status == ModelV2.EleStatus.ENABLED) {
            priceForListing
                    ?.prices
                    ?.find { qty >= it.minQty && qty <= it.maxQty }
                    ?.priceInPaisa?.defaultPrice
                    ?: priceForListing?.prices?.firstOrNull()?.priceInPaisa?.defaultPrice
                    ?: throw PricingMismatchException("NO pricing condition matches quanity $qty for sales units $salesUnit")
        } else {
            logger.info("of - returning price from catalg-service ${salesUnit.listingId}, sales unit state - ${salesUnit.status}")
            (salesUnit.priceDetails.priceConditionList
                    .find { priceCond -> qty >= priceCond.unitQtyMin && qty <= priceCond.unitQtyMax }
                    ?.unitPricePaise
                    ?: salesUnit.priceDetails.priceConditionList.firstOrNull()?.unitPricePaise
                    ?: throw PricingMismatchException("NO pricing condition matches quanity $qty for sales units $salesUnit"))
        }
    }

    private fun updateOrderLineQty(
        order: ModelV1.SellerOrder,
        orderLine: ModelV1.OrderLine,
        qty: Int,
        userId: String,
        conn: Handle
    ) {
        TelemetryScope.future(Dispatchers.IO) {
            val (perUnitPricePaise, maxUnits) =
                if (orderLine.itemType.equals(ModelV1.ItemType.LISTING)) {
                    val listing = catalogProvider.getListing(orderLine.listingId)
                    val priceForListing = pricingService.getContextualPrice(
                        orderLine.listingId,
                        pricingService.buildPricingContext(orderLine)
                    )
                    val listingAndPrice = pricingOptionsClient.getPricingOptionsV2(
                        (PricingOptionsRequest(
                            listing,
                            order.buyerId,
                            priceForListing
                        ))
                    )
                    val salesUnit =
                        listingAndPrice.tradeListing.salesUnitList.find { su -> su.salesUnitId == orderLine.salesUnitId }!!
                    val pricePerSaleUnit = listingAndPrice.priceForListing?.associateBy { it.saleUnitId } ?: emptyMap()
                    val perUnitPricePaise = unitPricePaise(salesUnit, qty, pricePerSaleUnit[salesUnit.salesUnitId])
                    val maxUnits = salesUnit.getMaxUnits(order.buyerId, listing.config.isManaged)
                    Pair(perUnitPricePaise, maxUnits)
                } else {
                    Pair(orderLine.perUnitSpPaise, Int.MAX_VALUE)
                }

            val newOrderLine = orderLine.toBuilder().apply {
                this.sellerOrderId = orderLine.sellerOrderId
                this.orderLineId = orderLine.orderLineId
                this.units = min(qty, maxUnits)
                this.perUnitSpPaise = perUnitPricePaise
                this.orderLineSpPaise = perUnitPricePaise * qty
                this.updatedBy = userId
            }.build()

            val sellerOrder = order.toBuilder().apply {
                this.orderId = order.orderId
                this.totalOrderSpPaise =
                    (order.totalOrderSpPaise - orderLine.orderLineSpPaise) + (perUnitPricePaise * qty)
            }.build()

            orderFormDAL.updateQtyOrderLine(newOrderLine, sellerOrder, conn)
        }.getSync()
    }

    private fun updateOrderLineProp(orderLine: ModelV1.OrderLine, request: OrderV2.ChangeOrderLineRequest, conn: Handle) {
        val sellerOrderLine = ModelV1.OrderLine.newBuilder().apply {
            this.orderLineId = orderLine.orderLineId
            this.sellerOrderId = orderLine.sellerOrderId
            this.properties = orderLine.properties
            this.updatedBy = request.userId
        }.build()

        conn.attach(SellerOrderLineDao.Update::class.java)
                .updateOrderLineProperties(sellerOrderLine)
    }

    private fun updateOrderLineInfo(orderLineId: String, request: OrderV2.ChangeOrderLineRequest): ModelV1.SellerOrder {
        val orderLine = dbi.onDemand(SellerOrderLineDao.Query::class.java).getOrderLine(orderLineId)
        val order = dbi.onDemand(com.udaan.om.dal.SellerOrderDao.Query::class.java).lookupOrder(orderLine.sellerOrderId)

        if (orderLine.properties.comboId.isNullOrEmpty().not()) {
            throw ComboException("You can't change the quantity of a Combo item")
        }

        if (order.orderStatus != StatesV1.SellerOrderState.SELLER_ORDER_DRAFT) {
            throw OrderNotFoundException("OrderLineId: $orderLineId")
        }

        dbi.inTransactionUnchecked { conn ->
            if (request.qty > 0) {
                updateOrderLineQty(order, orderLine, request.qty, request.userId, conn)
            }

            if (request.type == "item") {
                updateOrderLineProp(orderLine, request, conn)
            }
        }
        return order
    }

    private suspend fun getCodLimit(buyer: String): Long {
        return kotlin.runCatching {
            val codLimitConfig = businessConfigClient.getStringAsync(COD_LIMIT_EXEMPTIONS_KEY).await()?.let { config ->
                objectMapper.readValue(config, CodLimitConfig::class.java)
            }
            val codConfig = codLimitConfig?.codLimitList?.firstOrNull { it.buyers.contains(buyer) }
            codConfig?.let { it.limit } ?: MAX_COD_LIMIT
        }.getOrElse { e ->
            logger.error("Failed to load cod limit exemption config: ${e.message}", e)
            MAX_COD_LIMIT
        }
    }

    private suspend fun isCategoryAllowedForDeliveryCharge(categoryId: String): Boolean {
        return kotlin.runCatching {
            val allowedCategories = businessConfigClient.sMembersAsync<String>(
                BusinessConfigKey.DELIVERY_CHARGE_ALLOWED_CATEGORIES
            ).executeAwait()
            allowedCategories?.contains(categoryId) ?: false
        }.getOrElse { e ->
            logger.error("Failed to get allowed categories for delivery charge: ${e.message}", e)
            false
        }
    }

    private fun calculateDeliveryEstimate(shippingProfile: ShippingChargeProfile, route: ShippingV1.LogisticsRouteVslMaster,
                                          slaDetail: LaneSla, pointSla: Long): Pair<Int, Int> {
        val minHoursToDelivery: Int
        val maxHoursToDelivery: Int
        val hoursToDelivery = (pointSla - getCurrentMillis()).div(1000).div(60).div(60).toInt()
        when {
            listOf(ShippingChargeProfile.FRESH, ShippingChargeProfile.PHARMA, ShippingChargeProfile.FMCG, ShippingChargeProfile.MEAT).contains(shippingProfile) -> {
                minHoursToDelivery = 0
                maxHoursToDelivery = hoursToDelivery
            }
            listOf(ShippingChargeProfile.ENTERPRISE).contains(shippingProfile) -> {
                minHoursToDelivery = 2*24
                maxHoursToDelivery = 3*24
            }
            else -> {
//                val historicalSLA = if (route.srcPincode.isNumber() && route.destPincode.isNumber())
//                    promiseManager.getSla(route.srcPincode.toInt(),
//                            route.destPincode.toInt())
//                else
//                    null
//
//                val (min, max) = if (historicalSLA != null) {
//                    logger.info("historicalSLA = $historicalSLA")
//                    historicalSLA to historicalSLA + 2
//                } else {
//                    (slaDetail.sla_min ?: 0) to (slaDetail.sla_max ?: 0)
//                }

                logger.warn("calculateDeliveryEstimate(): Incorrect shipping profile used $shippingProfile")
                // dummy values
                minHoursToDelivery = 2*24
                maxHoursToDelivery = 3*24
            }
        }

        return Pair(minHoursToDelivery, maxHoursToDelivery)
    }

    private fun validateOrderLine(orderLine: ModelV1.OrderLine, listingMap: Map<String, ModelV2.TradeListing>) {
        if (orderLine.units <= 0) {
            logger.error("For Order ${orderLine.sellerOrderId} orderLine ${orderLine.orderLineId} " +
                "listing ${orderLine.listingId}, the unit count = ${orderLine.units}. Please verify if right " +
                "quantity is passed in split response")
            val listingTitle = listingMap[orderLine.listingId]?.let {
                it.generatedTitle.ifEmpty { it.title }
            } ?: orderLine.listingId
            throw OrderLineException("Order ${orderLine.sellerOrderId}: $listingTitle has invalid quantity")
        }
    }
    private fun updateOrderLine(shippingProfile: ShippingChargeProfile,
                                route: ShippingV1.LogisticsRouteVslMaster,
                                slaDetail: LaneSla,
                                orderLine: ModelV1.OrderLine,
                                paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
                                pointSla: Long,
                                pharmaAttributes: PharmaAttributes,
                                salesUnit: ModelV2.SalesUnit?,
                                categoryGroupId: String,
                                applyScheme: Boolean = false,
                                deliveryEstimateResponse: DeliveryEstimateResponse? = null,
    ): ModelV1.OrderLine {
        val (minHoursToDelivery: Int, maxHoursToDelivery: Int) = calculateDeliveryEstimate(shippingProfile, route, slaDetail, pointSla)

        logger.info("placeDraftOrder updateOrderLine buyerOrgId: {}, orderLine: {} salesUnit: {} assortmentNum: {}",
                orderLine.createdBy, orderLine, salesUnit, salesUnit?.assortmentDetails?.numItemsAssortment?:1)

        // Added temporary debugging statements to debug a case where esla is too large
        logger.info("[Invalid esla] For order ${orderLine.sellerOrderId} orderLineId ${orderLine.orderLineId} " +
                    "minHoursToDelivery = $minHoursToDelivery maxHoursToDelivery = $maxHoursToDelivery - " +
                    "shippingProfile = $shippingProfile, pointSla = $pointSla, slaDetail(min) = ${slaDetail.sla_min}" +
                    ", slaDetail(max) = ${slaDetail.sla_max}")

        val assortmentNum = if (salesUnit != null) {
            salesUnit.assortmentDetails.numItemsAssortment
        } else 1

        return orderLine.toBuilder()
                .apply {
                    this.miscInfo = miscInfo.toBuilder()
                            .setMinEslaHours(deliveryEstimateResponse?.deliveryEstimate?.minHoursToDeliver ?: minHoursToDelivery)
                            .setMaxEslaHours(deliveryEstimateResponse?.deliveryEstimate?.maxHoursToDeliver ?: maxHoursToDelivery)
                            .build()

                    this.orderLineStatus = StatesV1.SellerOrderLineState.SELLER_ORDER_LINE_RESERVED

                    // only handling credit pricing option
                    val p = this.pricingOptions
                    this.pricingOptions = p.toBuilder()
                            .also { builder ->
                                builder.clearPricingOptions()
                                builder.addAllPricingOptions(
                                        p.pricingOptionsList
                                                .filter { it.section == "CREDIT" }
                                                .map { paymentOption ->
                                                    if (paymentModes.find {
                                                                it == PaymentGatewayV1.PaymentInstrument.CREDIT ||
                                                                        it == PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER
                                                            } != null)
                                                        paymentOption
                                                    else
                                                        paymentOption.toBuilder().setAvailed(true).build()
                                                }
                                ).build()
                            }.build()

                    this.offerDetails = orderLine.offerDetails.toBuilder()
                            .apply {
                                if (shippingProfile == ShippingChargeProfile.PHARMA) {
                                    // Note: Don't update scheme, scheme update happens during reservation step
                                    // changing here based on current lot can lead to a different scheme in case of 
                                    // lot based split
                                    if (applyScheme) {
                                        this.pharmaScheme = pharmaAttributes.scheme
                                        // Note: To fix invoice issues
                                        this.discountBps = pharmaAttributes.cashDiscountBps.toInt()
                                        this.pharmaBuyerDiscountBps = pharmaAttributes.cashDiscountBps.toInt()
                                    }
                                    this.pharmaBuyerDiscountBps = this.discountBps
                                }
                                if (paymentModes.none {
                                            it == PaymentGatewayV1.PaymentInstrument.CREDIT ||
                                                    it == PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER
                                        }) {
                                    this.creditBuyerDiscountBps = 0
                                }

                            }.build()
                    this.properties = orderLine.properties.toBuilder().apply {
                        this.numOfAssortment = assortmentNum
                        this.categoryGroupId = categoryGroupId
                    }.build()
                }
                .build()
    }

    private fun validateCovid19(sellerOrg: OrgV1.OrgAccountExtendedResponse, buyerOrg: com.udaan.proto.models.ModelV1.OrgAccount) {
        val isInterCitySeller = isInterCitySeller(sellerOrg)
        val isEnterpriseBuyer = isEnterpriseBuyer(buyerOrg)

        if (isInterCitySeller && !isEnterpriseBuyer) {
            throw Covid19LockdownException()
        }
    }

    suspend fun getListingsMap(order: ModelV1.SellerOrder, isReplacementOrder: Boolean, isInternalFulfilmentOrder: Boolean): Map<String, ModelV2.TradeListing> {
        return if (!isReplacementOrder && !isInternalFulfilmentOrder) {
            CatalogBatchCall.getTradeListings(
                    order.orderLineList
                            .filter { it.itemType == ModelV1.ItemType.LISTING }
                            .map { ol -> ol.properties.listingId }
                            .distinct(), catalogProvider)
                    .await()
                    .associateBy { k ->
                        k.listingId
                    }
        } else {
            CatalogBatchCall.getTradeListings(
                    order.orderLineList
                            .filter {
                                it.itemType == ModelV1.ItemType.LISTING
                            }
                            .map { ol ->
                                ol.listingId
                            }
                            .toSet()
                            .toList(),
                    catalogProvider)
                    .await()
                    .associateBy { k -> k.listingId }
        }
    }

    private suspend fun getTaxLines(order: ModelV1.SellerOrder,
                    listingsMap: Map<String, ModelV2.TradeListing>,
                    isReplacementOrder: Boolean): List<TaxV1.TaxLine> {
        return if (!isReplacementOrder) {
            order.orderLineList
                .filter { it.itemType == ModelV1.ItemType.LISTING }
                .flatMap {
                    taxationBizLogic.getGoodsTaxDetails(it, listingsMap[it.properties.listingId]
                        ?: kotlin.run {
                            val listing = catalogProvider.getMinimalListing(it.properties.listingId)
                            logger.error("Inactive Listing(s): ${listing.listingId}")
                            throw ListingNotFoundException("Inactive Listing(s): " +
                                listing.let { it.generatedTitle.ifEmpty { it.title } })
                        }
                    )
                }
        } else {
            order.taxLineList!!
        }
    }

    private suspend fun validatePaymentLimit(paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
                                     order: ModelV1.SellerOrder,
                                     taxLines: List<TaxV1.TaxLine>) {
        val orderValueInPaisa = order.orderLineList.map { it.orderLineSpPaise }.sum() +
                taxLines.sumBy { it.taxAmountPaise.toInt() }
        val paymentLimit = checkPaymentLimit(paymentModes[0], orderValueInPaisa, order.buyerId)
        if (paymentLimit > 0) {
            val msg = "For " + paymentModes[0].name + " the maximum limit for order is " + paymentLimit.paiseToRsStr() + "."
            throw PaymentValueOutOfBoundsException(msg)
        }
    }

    private fun validateBuyerAndSellerOrgUnits(buyerOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
                                               sellerOrg: OrgV1.OrgAccountExtendedResponse,
                                               fromOrgUnitId: String?): Pair<com.udaan.proto.models.ModelV1.OrgUnit?, String> {
        if (buyerOrgUnit.unitAddress.addressLine1.isNullOrBlank()) {
            logger.error("Found org unit without sufficient info ${buyerOrgUnit.orgUnitId}")
            throw InvalidAddressException("Address does not have any location information. Please select a valid address")
        }
        val sellerOrgUnitId = if (fromOrgUnitId.isNullOrEmpty()) sellerOrg.orgAccount.headOfficeOrgUnitRef else fromOrgUnitId

        val sellerOrgUnit = sellerOrg.orgAccount.orgUnitsMap[sellerOrgUnitId]
        if (sellerOrgUnit == null) {
            logger.error("Seller's org unit not found - $sellerOrgUnitId for buyer - ${buyerOrgUnit.orgId}")
            throw InvalidOrgUnitException("Seller's org unit is inactive")
        }
        return Pair(sellerOrgUnit, sellerOrgUnitId)
    }

    private fun validateShippingProfile(orderId: String,
                                                shippingProfile: ShippingChargeProfile,
                                                route: ShippingV1.LogisticsRouteVslMaster,
                                                paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
                                                buyerOrgUnitId: String,
                                                sellerOrgUnitId: String,
                                                buyerPincode: String): CompletableFuture<Unit> {
        return TelemetryScope.future {
            if (shippingProfile.allowOnlyProfileCheck()) {
                //For those shipping profiles that allow only profile checks, we can check for availability of service providers pre-order placement,
                //so as to remove ODA possibility
                val (providerAvailable, logisticServiceDeniedReason, message) = TelemetryScope.async(Dispatchers.IO) {
                    checkForServiceProviders(
                        orderId,
                        route,
                        paymentModes,
                        shippingProfile,
                        buyerOrgUnitId,
                        sellerOrgUnitId,
                        buyerPincode
                    )
                }.await()

                if (!providerAvailable) throw ProviderUnavailableException(message ?: "Some exception occurred")
            }
        }
    }

    private suspend fun findPrepaidDeliveryCharges(
        sellerOrder: ModelV1.SellerOrder,
        sellerOrg: OrgV1.OrgAccountExtendedResponse,
        paymentInstrument: List<PaymentGatewayV1.PaymentInstrument>,
        profile: ShippingChargeProfile,
        toOrgUnitId: String,
        route: ShippingV1.LogisticsRouteVslMaster,
    ): CompletableFuture<Long> {
        return TelemetryScope.future(Dispatchers.IO) {
            val logisticService = getLogisticsServiceType(paymentInstrument.map {
                ModelV1.OrderPayments.newBuilder().apply { this.paymentMethod = it }.build()
            })
            val providers = logisticsProvider.findProviderDetails(route, logisticService)
            // Check if there is only one org and that is 3PL prepayment type and apply delivery charges only for footwear
            if (providers.size == 1 && logisticsProvider.applyPrepaidDeliveryCharges(providers.first(), profile)) {
                val categoryTree = shippingProfileSelector.getCategoryTree(sellerOrder.orderLineList, sellerOrg)
                // TODO: fix freeshipping flags
                val rateCardResponse = logisticsProvider.findRateCards(
                    LogisticsRateCardContext(
                        sellerOrder = sellerOrder,
                        profile = profile,
                        route = route,
                        toOrgUnitId = toOrgUnitId,
                        categoryTree = categoryTree,
                        listingIds = sellerOrder.orderLineList.map { it.listingId }.toSet()
                    )
                )
                return@future rateCardResponse.nationalFixedBaseDeliveryChargeInPaise + rateCardResponse.nationalOdaDeliveryChargeInPaise
            }
            return@future 0L
        }
    }

    private suspend fun validateSelectedDeliverySlot(
        fulfillmentCenter: StatesV1.FulfillmentCenter,
        shippingProfile: ShippingChargeProfile,
        sellerId: String,
        toOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
        selectedDeliverySlotId: String?,
        selectedItemLevelDeliverySlot: Map<String, String>?,
        orderId: String,
        platform: SellingPlatform,
        orderLineDeliverySlotValidationLines: List<OrderLineDeliverySlotValidationLine>
    ) = withContext(Dispatchers.IO) {
        try {
            if (!selectedItemLevelDeliverySlot.isNullOrEmpty() && orderLineDeliverySlotValidationLines.isNotEmpty()) {
                // selectedItemLevelDeliverySlot is map containing delivery slots for all items in the primary order
                // If order would've been split, slots for only those listings in the split order should be validated
                val items = orderLineDeliverySlotValidationLines.map { entry ->
                    ValidateDeliverySlotsRequestItem(
                        itemId = entry.salesUnitId, // SalesUnitId is the key
                        listingId =  entry.listingId,
                        salesUnitId = entry.salesUnitId,
                        fulfillmentCenter = fulfillmentCenter,
                        promisedInventoryTierType = entry.tier,
                        selectedDeliverySlotId = selectedItemLevelDeliverySlot[entry.salesUnitId]
                            ?: let {
                                logger.error("Order id: $orderId, ValidateSelectedDeliverySlot, No slot found for $entry")
                                throw SlaNotFoundException("There is a change in offered delivery time, Please refresh the cart and retry")
                            }
                    )
                }
                val response = deliverySlotsServiceClient.validateDeliverySlots(
                    ValidateDeliverySlotsRequest(
                        sellerOrgId = sellerId,
                        buyerOrgId = toOrgUnit.orgId,
                        buyerPincode = toOrgUnit.unitAddress.pincode,
                        buyerOrgUnitId = toOrgUnit.orgUnitId,
                        sellingPlatform = platform,
                        items = items
                    )
                ).executeAwaitWithEachTimeOutAndRetries(eachTimeoutMillis = 3000L, retries = 2)
                logger.info("validateSelectedDeliverySlot Item Level returned ${response.offeredSlotsMap} for orderId: $orderId")

                // Check if true has been returned for each item request
                val isValid = response.offeredSlotsMap.all {
                    it.value.validationResult
                }
                val slotIds: Set<String> = response.offeredSlotsMap.values.groupBy { it.slotId }.keys

                if (isValid && slotIds.size == 1) {
                    return@withContext slotIds.first()
                } else {
                    logger.error("Order id: $orderId, ValidateSelectedDeliverySlot, Item Level Selected delivery slot: $selectedItemLevelDeliverySlot does not match Offered delivery slot: $slotIds")
                    throw SlaNotFoundException("There is a change in offered delivery time, Please refresh the cart and retry")
                }
            } else if (!selectedDeliverySlotId.isNullOrBlank()) {
                val offeredDeliverySlot = deliverySlotsServiceClient.getAppliedDeliverySlot(
                    GetAppliedDeliverySlotRequest(
                        scCategory = OrchestratorUtils.getScCategoryFromShippingProfile(shippingProfile),
                        sellerOrgId = sellerId,
                        buyerOrgId = toOrgUnit.orgId,
                        buyerPincode = toOrgUnit.unitAddress.pincode,
                        buyerOrgUnitId = toOrgUnit.orgUnitId,
                        isUdaanFulfilled = fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UDWH,
                        isFpJit = fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UDFPJIT,
                        selectedDeliverySlotId = selectedDeliverySlotId,
                        orderId = orderId,
                        sellingPlatform = platform
                    )
                ).executeAwaitWithEachTimeOutAndRetries(eachTimeoutMillis = 3000L, retries = 2).data
                logger.info("validateSelectedDeliverySlot returned $offeredDeliverySlot for orderId: $orderId")
                if (offeredDeliverySlot != selectedDeliverySlotId) {
                    logger.error("Order id: $orderId, ValidateSelectedDeliverySlot, Selected delivery slot: $selectedDeliverySlotId does not match Offered delivery slot: $offeredDeliverySlot")
                    throw SlaNotFoundException("There is a change in offered delivery time, Please refresh the cart and retry")
                }
                return@withContext offeredDeliverySlot
            } else {
                return@withContext null
            }
        } catch (e: Exception) {
            when (e) {
                is SlaNotFoundException -> throw e
                else -> {
                    logger.error(
                        "Order id: $orderId, Exception while validateSelectedDeliverySlot, e = ${e.message}",
                        e
                    )
                    throw SlaNotFoundException("Unable to determine order delivery time, Please refresh the cart and retry")
                }
            }
        }
    }

    private fun findPointSla(
        fulfillmentCenter: StatesV1.FulfillmentCenter,
        shippingProfile: ShippingChargeProfile,
        sellerId: String,
        toOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
        selectedDeliverySlotId: String?,
        orderId: String,
        platform: SellingPlatform
    ): CompletableFuture<Long> {
        logger.info(
            "Calling findSla - fulfillmentCenter : $fulfillmentCenter shippingProfile : $shippingProfile " +
                "sellerId : $sellerId toOrgUnit : $toOrgUnit selectedDeliverySlotId : $selectedDeliverySlotId " +
                "orderId: $orderId platform: ${platform.name}"
        )
        return TelemetryScope.future(Dispatchers.IO) {
            try {
                val slaResp = promiseServiceClient.findSla(
                    SlaPromiseRequest(
                        scCategory = OrchestratorUtils.getScCategoryFromShippingProfile(shippingProfile),
                        sellerOrgId = sellerId,
                        buyerOrgId = toOrgUnit.orgId,
                        buyerPincodes = listOf(toOrgUnit.unitAddress.pincode),
                        buyerOrgUnitIds = listOf(toOrgUnit.orgUnitId),
                        isUdaanFulfilled = fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UDWH,
                        isFpJit = fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UDFPJIT,
                        selectedDeliverySlotId = selectedDeliverySlotId,
                        platformId = platform
                    )
                ).executeAwaitWithEachTimeOutAndRetries(eachTimeoutMillis = 3000L, retries = 2)
                slaResp.data.get(toOrgUnit.unitAddress.pincode)?.expectedTimestampToDeliver ?: 0L
            } catch (e: Exception) {
                logger.error("Order id: $orderId, Exception while calling findSla, e = ${e.message}", e)
                throw SlaNotFoundException("Unable to determine order delivery time, Please retry")
            }
        }
    }

    private suspend fun prepareAllLevies(
        sellerOrder: ModelV1.SellerOrder,
        sellerGstin: String?,
        sellerPincode: String?,
        buyerGstin: String?,
        buyerPincode: String?,
        orderLines: List<ModelV1.OrderLine>,
        listingMap: Map<String, ModelV2.TradeListing>
    ): List<OrderLineLevy> {
        if (sellerGstin.isNullOrBlank() && sellerPincode.isNullOrBlank()) {
            logger.error("[Pincode&Gstin]Unable to generate levies for seller: ${sellerOrder.sellerId}, " +
                    "buyerId: ${sellerOrder.buyerId}, sellerGstin: $sellerGstin, sellerPincode: $sellerPincode" +
                    "buyerGstin: $buyerGstin, buyerPincode: $buyerPincode")
            throw InvalidOrgUnitException("Seller's address is incomplete")
        }
        try {
            val supplyType = invoicingServiceClient.getGoodsSupplyType(
                    sellerGSTIN = sellerGstin ?: "",
                    sellerPincode = sellerPincode?: "",
                    buyerGSTIN = buyerGstin ?: "",
                    buyerPincode = buyerPincode ?: ""
            ).executeAwait()
            return taxationBizLogic.getAllLeviesForOrder(sellerOrder, listingMap, supplyType.supplyType)
        } catch (e: Exception) {
            logger.error("Unable to generate levies for seller: ${sellerOrder.sellerId}, buyerId: ${sellerOrder.buyerId}")
            logger.error("Failed to generate levies: $e")
            throw TaxGenerationException("Unable to generate taxes, please try again")
        }
    }

    private suspend fun profileOrder(
        order: ModelV1.SellerOrder,
        shipToOrgUnitId: String,
        listingMap: Map<String, ModelV2.TradeListing>
    ): ModelV1.SellerOrder {
        return if (order.extraData.selectedPaymentMethod == PaymentGatewayV1.PaymentInstrument.COD &&
                order.orderLineCount > 0) {
            try {
                val categoryGroupId = order.extraData.categoryGroupId
                if (categoryGroupId.isEmpty()) {
                    logger.info("Empty category group id for ${order.orderId} and buyer ${order.buyerId}")
                    order
                } else {
                    val categories = listingMap.values.parallelMap { listing ->
                        categoryConfigHelper.getCategory(listing, null, null)
                    }.filterNotNull().toSet().toList()
                    val orderMetadata = OrderMetaData(
                        numOrderLines = order.orderLineCount,
                        orderQuantity = order.orderLineList.map { it.units }.sum(),
                        orderTimestamp = System.currentTimeMillis(),
                        sellerId = order.sellerId,
                        totalAmountInPaise = order.orderValueInPaisa(),
                        verticalIds = listingMap.values.map { listing -> listing.vertical }.distinct()
                            .filterNotNull()
                            .filter { x: String? -> x != "" }
                    )
                    val orderToBeSentForApprovalResponse = orderProfilingClient.performOrderRiskV2checks(
                        OrderRiskV2Request(
                            orderId = order.orderId,
                            buyerOrgId = order.buyerId,
                            categoryGroupId = categoryGroupId,
                            metaData = orderMetadata,
                            categoryIds = categories,
                            shipToOrgUnitId = shipToOrgUnitId
                        )
                    ).executeAwait()

                    if (orderToBeSentForApprovalResponse.orderOnApproval) {
                        logger.info("${order.buyerId} is on probation for order ${order.orderId} with reason ${orderToBeSentForApprovalResponse.orderOnApprovalReason?.name ?: "NA"}")
                        order.toBuilder().apply {
                            this.extraData = order.extraData.toBuilder().setBuyerProbationReason(orderToBeSentForApprovalResponse.orderOnApprovalReason?.name ?: "").build()
                            this.orderStatus = StatesV1.SellerOrderState.SELLER_ORDER_APPROVAL_PENDING
                        }.build()
                    } else {
                        order
                    }
                }
            } catch (e: Exception) {
                logger.error("[${order.orderId}] Failed to validated order hold status(${order.buyerId}) probation: ", e)
                order
            }
        } else {
            order
        }
    }

    // Note: This is a temporary fix to b
    private suspend fun validateSellerGSTIN(sellerOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit, taxLines: List<TaxV1.TaxLine>) {
        try {
            if (sellerOrgUnit.gstin.isNullOrBlank()) {
                val exemptionStatus = orgServiceClient.orgGSTExemptStatus(sellerOrgUnit.orgId).executeAwait()
                if ((exemptionStatus.data == ExemptStatus.EXEMPTED && taxLines.any { it.taxAmountPaise > 0L })
                        || exemptionStatus.data != ExemptStatus.EXEMPTED) {
                    logger.error("GSTIN is incomplete for ${sellerOrgUnit.orgId} - ${sellerOrgUnit.orgUnitId}")
                    throw InvalidOrgUnitException("Invalid seller org, seller is blocked due to insufficient info")
                }
            }
        } catch (e: InvalidOrgUnitException)  {
            throw e
        } catch (e: Exception) {
            logger.error("Failed to validate seller's GSTIN ${sellerOrgUnit.orgId}: ", e)
        }
    }

    fun createFulfilmentLineFor(order: ModelV1.SellerOrder,
                                fromOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
                                toOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
                                fulfillmentCenter: StatesV1.FulfillmentCenter): ModelV1.FulfillmentLine? {
        val ff = fulfillmentBizLogicV2
                .createFulfillmentLineForOrderLineObj(fromOrgUnit, order.orderId, toOrgUnit, fulfillmentCenter)

        val inv = order.orderLineList
                .map { orderLine ->
                    fulfillmentBizLogicV2.createInventoryReservationObj(ff.fulfillmentId, orderLine, ff.shipfromAddressId)
                }

        val entries = fulfillmentBizLogicV2.createFulFillmentLineItemObjs(inv)

        return ff.toBuilder()
                .addAllOrderlineQty(inv)
                .addAllFulfillmentLineItems(entries)
                .build()
    }

    fun createFulfilmentLineWithOrderLines(order: ModelV1.SellerOrder,
                                           orderLines: List<ModelV1.OrderLine>,
                                           fromOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
                                           toOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit,
                                           fulfillmentCenter: StatesV1.FulfillmentCenter): ModelV1.FulfillmentLine? {
        val ff = fulfillmentBizLogicV2
                .createFulfillmentLineForOrderLineObj(fromOrgUnit, order.orderId, toOrgUnit, fulfillmentCenter)

        val inv = orderLines.map { orderLine ->
                    fulfillmentBizLogicV2.createInventoryReservationObj(ff.fulfillmentId, orderLine, ff.shipfromAddressId)
                }

        val entries = fulfillmentBizLogicV2.createFulFillmentLineItemObjs(inv)

        return ff.toBuilder()
                .addAllOrderlineQty(inv)
                .addAllFulfillmentLineItems(entries)
                .build()
    }

    private suspend fun fetchPricingContext(
        buyerOrg: com.udaan.proto.models.ModelV1.OrgAccount,
        order: ModelV1.SellerOrder,
        toOrgUnitId: String,
        listingsMap: Map<String, ModelV2.TradeListing>
    ): PriceContext? {
        val pincode = buyerOrg.orgUnitsMap[toOrgUnitId]?.unitAddress?.pincode ?: ""
        return if (order.extraData.categoryGroupId == CategoryGroupV2.Pharma.id) {
            val listingSalesUnitRef = order.orderLineList.map { orderLine ->
                ListingSalesUnitRef(
                    listingId = orderLine.listingId,
                    salesUnitId = orderLine.salesUnitId,
                    refId = orderLine.orderLineId,
                    lotId = orderLine.miscInfo.ffSkuId
                )
            }
            val request = PriceRequest(
                orgId = order.buyerId,
                orgUnitId = toOrgUnitId,
                pincode = pincode,
                listingSalesUnitRef = listingSalesUnitRef,
                listingsMap = listingsMap
            )
            val listingSalesUnitRefMap = listingSalesUnitRef.map { ref ->
                (ref.refId ?: "") to (ref.lotId ?: "")
            }
            Telemetry.trackEvent(
                name = priceEvent,
                properties = mapOf(
                    "orderId" to order.orderId,
                    "orgId" to request.orgId,
                    "orgUnitId" to (request.orgUnitId ?: ""),
                    "pincode" to (request.pincode ?: ""),
                    "listingSalesUnitRef" to listingSalesUnitRefMap.toString(),
                )
            )
            priceProvider.fetchLotPrice(
                request
            )
        } else null
    }

    // Used only by pacman, handle group online payments
    suspend fun placeMultipleOrders(
        placeOrderRequests: List<PlaceOrderReq>,
        conn: Handle,
    ): List<ModelV1.SellerOrder> {
        val placeOrderPlans = prepareMultipleOrderPlan(placeOrderRequests)
        return placeOrderPlans.map { placeOrderData ->
            executePlaceData(
                placeOrderData,
                conn,
                savePrepayment = true,
            )
        }
    }

    suspend fun prepareMultipleOrderPlan(
        placeOrderRequests: List<PlaceOrderReq>,
    ): List<PlaceOrderPlan> {
        val mainOrderId = mainOrderIdGenerator.nextId()
        val listingSalesUnitIds =
            placeOrderRequests.map { it.order }.flatMap {
                it.orderLineList.map { it.listingId to it.salesUnitId }
            }.groupBy { it.first }.map { (listingId, salesUnitsList) ->
                listingId to salesUnitsList.map { it.second }
            }
        val listingsMap = catalogProvider.getMultipleListings(listingSalesUnitIds).associateBy { it.listingId }
        val buyerOrgUnit = orgRepository.getOrgUnit(placeOrderRequests.first().toOrgUnitId).await()
        val orders = placeOrderRequests.map { req ->
            val order = req.order
            val sellerOrgUnit = req.shipFromOrgUnit ?: (req.fromOrgUnitId?.let { fromOrgUnitId ->
                if (fromOrgUnitId.isNotBlank()) {
                    orgRepository.getOrgUnit(fromOrgUnitId).await()
                } else {
                    val sellerOrg = orgRepository.getOrg(order.sellerId).await()
                    sellerOrg.orgUnitsMap[sellerOrg.headOfficeOrgUnitRef]
                }
            } ?: run {
                val sellerOrg = orgRepository.getOrg(order.sellerId).await()
                sellerOrg.orgUnitsMap[sellerOrg.headOfficeOrgUnitRef]
            })
            val levies = prepareAllLevies(
                order,
                sellerOrgUnit?.gstin,
                sellerOrgUnit?.unitAddress?.pincode,
                buyerOrgUnit.gstin,
                buyerOrgUnit.unitAddress.pincode,
                order.orderLineList,
                listingsMap
            )
            order.toBuilder().clearOrderLineLevy().addAllOrderLineLevy(levies.toOMSOrderLineLevies()).build()
        }
        val prepaymentData = riskDetector.prepaymentRequired(
            sellerOrder = orders.first(),
            listingMap = listingsMap,
            buyerSelectedPaymentModes = placeOrderRequests.first().paymentModes,
            deliveryCharges = 0L,
            multipleOrders = orders
        )
        val originalOrderValue = orders.sumOf { it.orderValueInPaisa() }
        val placeOrderPlans = placeOrderRequests.map { request ->
            preparePlaceOrderPlan(
                request.order.toBuilder().setMainOrderId(mainOrderId).build(),
                request.toOrgUnitId,
                request.paymentModes,
                request.fromOrgUnitId,
                request.instruction,
                request.needgstin,
                request.isReplacementOrder,
                request.fulfillmentCenter,
                request.orderLinesToDiscard,
                request.orderLinesToAdd,
                request.selectedDeliverySlotId,
                request.selectedOnlinePaymentType,
                request.billToOrgUnitId,
                request.isInternalFulfilmentOrder,
                request.promiseInfo,
                request.pointSlaOverride,
                request.shipFromOrgUnit,
                preCalculatedPrepaymentData = prepaymentData.await(),
                originalOrderValue = originalOrderValue,
                selectedItemLevelDeliverySlot = request.selectedItemLevelDeliverySlot
            )
        }
        logger.info(
            "placeOrderPlans: ${
                placeOrderPlans.map {
                    Triple(
                        it.sellerOrder.orderId,
                        it.sellerOrder.orderValueInPaisa(),
                        it.sellerOrder.extraData.prepaymentAmountInPaisa
                    )
                }
            }, total token amount: ${placeOrderPlans.sumOf { it.sellerOrder.totalTokenAmount() }}"
        )
        val prepaymentRequestDto = placeOrderPlans.filter { placeOrderData ->
            placeOrderData.hasPrepayment
        }.map { placeOrderPlan ->
            PrepaymentRequestDTO(
                sourceRefId = placeOrderPlan.sellerOrder.orderId,
                sellerOrgId = placeOrderPlan.sellerOrder.sellerId,
                itemAmountInPaisa = placeOrderPlan.sellerOrder.totalTokenAmount(),
                paymentPurpose = if (placeOrderPlan.isAdvancePayment) {
                    PrepaymentPurposeType.ADVANCE_PAYMENT
                } else {
                    PrepaymentPurposeType.TOKEN_PREPAYMENT
                },
            )
        }
        logger.info("prepaymentRequestDto: $prepaymentRequestDto")
        val updatedOrderPlans = if (prepaymentRequestDto.isNotEmpty()) {
            val groupPaymentResponse = paymentServiceClient.createGroupPrepaymentEntries(
                CreateGroupPrepaymentRequest(
                    buyerOrgId = placeOrderPlans.first().sellerOrder.buyerId,
                    prepaymentRequests = prepaymentRequestDto
                )
            ).executeAwait()
            logger.info("[groupPaymentResponse]: $groupPaymentResponse")
            placeOrderPlans.map { placeOrderData ->
                if (placeOrderData.hasPrepayment) {
                    val extraData = placeOrderData.sellerOrder.extraData.toBuilder()
                        .setPaymentId(groupPaymentResponse.groupId).build()
                    val updatedOrder = placeOrderData.sellerOrder.toBuilder().setExtraData(extraData).build()
                    placeOrderData.copy(sellerOrder = updatedOrder)
                } else placeOrderData
            }
        } else placeOrderPlans
        logger.info("updatedOrderPlans: $updatedOrderPlans")
        return updatedOrderPlans
    }

    fun placeDraftOrder(
        order: ModelV1.SellerOrder,
        toOrgUnitId: String,
        paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
        fromOrgUnitId: String? = null,
        instruction: String? = "",
        needgstin: Boolean = true,
        isReplacementOrder: Boolean = false,
        fulfillmentCenter: StatesV1.FulfillmentCenter = StatesV1.FulfillmentCenter.FFC_SELLER,
        orderLinesToDiscard: List<ModelV1.OrderLine> = emptyList(),
        orderLinesToAdd: List<ModelV1.OrderLine> = emptyList(),
        selectedDeliverySlotId: String?,
        selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
        billToOrgUnitId: String = "",
        isInternalFulfilmentOrder: Boolean = false,
        promiseInfo: PromiseInfoResponse? = null,
        pointSlaOverride: Long? = null,
        shipFromOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit? = null,
        originalOrderValue: Long,
        conn: Handle
    ): CompletableFuture<ModelV1.SellerOrder> {
        //check if new order --replacement order
        return TelemetryScope.future(Dispatchers.IO) {
            val placeOrderData = preparePlaceOrderPlan(
                order,
                toOrgUnitId,
                paymentModes,
                fromOrgUnitId,
                instruction,
                needgstin,
                isReplacementOrder,
                fulfillmentCenter,
                orderLinesToDiscard,
                orderLinesToAdd,
                selectedDeliverySlotId,
                selectedOnlinePaymentType,
                billToOrgUnitId,
                isInternalFulfilmentOrder,
                promiseInfo,
                pointSlaOverride,
                shipFromOrgUnit,
                originalOrderValue = originalOrderValue,
                selectedItemLevelDeliverySlot = null
            )
            executePlaceData(
                placeOrderData,
                conn
            )
        }
    }

    fun executePlaceData(
        placeOrderPlan: PlaceOrderPlan,
        conn: Handle,
        savePrepayment: Boolean = true,
    ): ModelV1.SellerOrder {
        val sellerOrderQDao = conn.attach(SellerOrderDao.Query::class.java)
        val isNewOrder = sellerOrderQDao.lookupOrder(placeOrderPlan.sellerOrder.orderId) == null
        if (isNewOrder) {
            Telemetry.trackEvent(
                "Order-form-new-order",
                mapOf("orderId" to placeOrderPlan.sellerOrder.orderId),
                mapOf()
            )
        }
        val sellerOrder = placeOrderPlan.sellerOrder
        val blockIds = orderPaymentHandler.block(
            placeOrderPlan.sellerOrder,
            placeOrderPlan.paymentModes,
            sellerOrder.orderValueInPaisa(),
            isPrepaidOnlyOrder(placeOrderPlan.listingMap),
            conn
        )
        val extraDataBuilder = sellerOrder.extraData.toBuilder()
        if (blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT].isNullOrEmpty().not()) {
            extraDataBuilder.creditBlockId = blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT]
        }

        if (blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER].isNullOrEmpty().not()) {
            extraDataBuilder.creditBlockId = blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER]
        }

        val updatedOrder = sellerOrder.toBuilder().setExtraData(extraDataBuilder.build()).build()
        orderFormDAL.placeOrderForm(
            sellerOrder = updatedOrder,
            riskyTransaction = if (savePrepayment) placeOrderPlan.hasPrepayment else false,
            isNewOrder = isNewOrder,
            discardedOrderLines = placeOrderPlan.discardedOrderLines,
            newOrderLines = placeOrderPlan.newOrderLines,
            orderLineLevies = placeOrderPlan.orderLineLevies,
            sellerOrderEta = placeOrderPlan.sellerOrderEta,
            promoLines = placeOrderPlan.promoLines,
            rewardLine = placeOrderPlan.rewardLine,
            orderCharge = placeOrderPlan.orderCharge,
            orderLineCharges = placeOrderPlan.orderLineCharges,
            conn = conn
        )
        return updatedOrder
    }

    suspend fun executePlacePlans(
        conn: Handle,
        placeOrderPlans: List<PlaceOrderPlan>,
        savePrepayment: Boolean = true,
        orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>? = null,
        orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>? = null
    ): PlaceOrderResponse {
        val mainOrderId = mainOrderIdGenerator.nextId()
        val categoryGroupId = placeOrderPlans.first().sellerOrder.extraData.categoryGroupId
        val placedOrders = placeOrderPlans.map { placeOrderPlan ->
            val orderDeliveryChargeData = orderDeliveryChargeDataMap?.get(placeOrderPlan.sellerOrder.orderId)
            val orderLineDeliveryChargeDataList =
                orderLineDeliveryChargeDataMap?.get(placeOrderPlan.sellerOrder.orderId)
            val deliveryChargeId = orderDeliveryChargeData?.let { orderDeliveryChargeDataObj ->
                val isCategoryAllowedForDeliveryCharge = isCategoryAllowedForDeliveryCharge(categoryGroupId)
                val orderDeliveryChargeObj =
                    OrderDeliveryChargeFactory.create(orderDeliveryChargeDataObj, categoryGroupId, isCategoryAllowedForDeliveryCharge)
                orderDeliveryChargeObj?.let { orderDeliveryCharge ->
                    orderFormDAL.storeOrderDeliveryCharge(orderDeliveryCharge, conn)
                    orderLineDeliveryChargeDataList?.let { orderLineDeliveryChargeDataObjList ->
                        orderLineDeliveryChargeDataObjList.map { orderLineDeliveryChargeDataObj ->
                            val orderLineDeliveryChargeObj = OrderLineDeliveryChargeFactory.create(
                                orderLineDeliveryChargeDataObj, orderDeliveryCharge.id, categoryGroupId
                            )
                            orderLineDeliveryChargeObj?.let {
                                orderFormDAL.storeOrderLineDeliveryCharge(it, conn)
                            }
                        }
                    }
                }
                orderDeliveryChargeObj?.id
            }
            val extraDataBuilder = placeOrderPlan.sellerOrder.extraData.toBuilder()
            if (null != deliveryChargeId) {
                extraDataBuilder.setOrderDeliveryChargeId(deliveryChargeId)
            }
            val updatedOrder = placeOrderPlan.sellerOrder.toBuilder().setMainOrderId(mainOrderId)
                .setExtraData(extraDataBuilder.build()).build()
            executePlaceData(placeOrderPlan.copy(sellerOrder = updatedOrder), conn, savePrepayment = savePrepayment)
        }
        return PlaceOrderResponse(placedOrders, null)
    }

    suspend fun preparePlaceOrderPlan(
        order: ModelV1.SellerOrder,
        toOrgUnitId: String,
        paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
        fromOrgUnitId: String? = null,
        instruction: String? = "",
        needgstin: Boolean = true,
        isReplacementOrder: Boolean = false,
        fulfillmentCenter: StatesV1.FulfillmentCenter = StatesV1.FulfillmentCenter.FFC_SELLER,
        orderLinesToDiscard: List<ModelV1.OrderLine> = emptyList(),
        orderLinesToAdd: List<ModelV1.OrderLine> = emptyList(),
        selectedDeliverySlotId: String?,
        selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
        billToOrgUnitId: String = "",
        isInternalFulfilmentOrder: Boolean = false,
        promiseInfo: PromiseInfoResponse? = null,
        pointSlaOverride: Long? = null,
        shipFromOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit? = null,
        preCalculatedPrepaymentData: PrepaymentData? = null,
        originalOrderValue: Long,
        rewardLine: RewardLine? = null,
        selectedItemLevelDeliverySlot: Map<String, String>?,
    ): PlaceOrderPlan {
        try {
            val buyerOrgId = order.buyerId
            val buyerOrgFuture = orgRepository.getOrg(buyerOrgId)
            val sellerOrgFuture = orgRepository.getOrgExtended(order.sellerId)

            val sellerOrg = sellerOrgFuture.await()
            val buyerOrg = buyerOrgFuture.await()

            if (fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_SELLER) {
                validateSellerAvailability(sellerOrg)
            }

            val listingMap = getListingsMap(order, isReplacementOrder, isInternalFulfilmentOrder)

            logger.info("placeDraftOrder buyerOrgId: {}", buyerOrgId)
            logger.info("fromOrgUnitId: $fromOrgUnitId, shipFromOrgUnit: ${shipFromOrgUnit?.orgUnitId}")

            val taxLine = getTaxLines(order, listingMap, isReplacementOrder)

            validatePaymentLimit(paymentModes, order, taxLine)

            val toOrgUnit = kotlin.runCatching {
                orgRepository.getOrgUnit(toOrgUnitId).await()
            }.getOrElse { exception ->
                logger.error(
                    "Failed to find org unit of the buyer(${buyerOrg.orgId}) - $toOrgUnitId: ${exception.message}",
                    exception
                )
                throw InvalidAddressException("Delivery address not found")
            }
            if (toOrgUnit.orgId != buyerOrgId) {
                logger.error("Org unit org id is ${toOrgUnit.orgId} and buyer org id: $buyerOrgId")
                throw InvalidOrgUnitException("Buyer and requested address are not matching")
            }

            val sellerOrgExtended = sellerOrgFuture.await()
            val (fromOrgUnit, sellerOrgUnitId) = validateBuyerAndSellerOrgUnits(
                toOrgUnit,
                sellerOrgExtended,
                fromOrgUnitId
            )

            fromOrgUnit?.let {
                validateSellerGSTIN(fromOrgUnit, taxLine)
            }

            Telemetry.trackEvent(
                "TEMP_EVENT_SELLER_ORDER_PLACEMENT_HO",
                mapOf(
                    DIM_FF_ORDER_PLACEMENT_ORDER_ID to (order.orderId ?: ""),
                    "OrgUnitId" to fromOrgUnit!!.orgUnitId!!,
                    "OrigIdIfAny" to (sellerOrgUnitId ?: ""),
                    "FF_From" to fromOrgUnitId!!
                ),
                mapOf()
            )

            val shippingProfile = logisticsProvider.getShippingProfile(
                order.orderLineList.extractListingIds(),
                sellerOrgExtended.orgAccount.orgId,
                buyerOrg.orgId
            ).shippingChargeProfile

            val fromOrgUnitForFfLine = when {
                fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UD_IN_TRANSIT_JIT -> shipFromOrgUnit
                shippingProfile == ShippingChargeProfile.FMCG
                        && fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UDFPJIT -> shipFromOrgUnit
                else -> fromOrgUnit
            }

            logger.info(
                "[preparePlaceOrderPlan] orderId=${order.orderId} sellerId=${order.sellerId} " +
                        "shippingProfile=$shippingProfile fulfillmentCenter=$fulfillmentCenter " +
                        "fromOrgUnitForFfLine=${fromOrgUnitForFfLine?.orgUnitId}"
            )

            val fulfillmentLine = createFulfilmentLineFor(
                order = order,
                fromOrgUnit = fromOrgUnitForFfLine ?: throw InvalidOrgUnitException("FF Lines creation failed. Please try again in some time"),
                toOrgUnit = toOrgUnit,
                fulfillmentCenter = fulfillmentCenter
            )

            if (isInterCitySeller(sellerOrg) && !isEnterpriseBuyer(buyerOrg)) {
                val categoryTree = shippingProfileSelector.getCategoryTree(order.orderLineList, sellerOrg)
                validateBlockedSeller(sellerOrg, fulfillmentCenter != StatesV1.FulfillmentCenter.FFC_SELLER)
                validateBlockedServiceability(buyerOrg, toOrgUnit, sellerOrg, categoryTree).await()
            }

            val fromPincode = if (fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UD_IN_TRANSIT_JIT) {
                logger.info("Fetching from pincode using shipFromOrgUnit")
                shipFromOrgUnit?.let { it.unitAddress.pincode }
                    ?: throw InvalidOrgUnitException("Fetching fromPincode failed. Please try again in some time")
            } else fromOrgUnit.unitAddress.pincode

            val sourceOrgUnit = if (fulfillmentCenter == StatesV1.FulfillmentCenter.FFC_UD_IN_TRANSIT_JIT) {
                shipFromOrgUnit ?: throw InvalidOrgUnitException("Fetching fromPincode failed. Please try again in some time")
            } else fromOrgUnit

            val routeSla = logisticsProvider.getRoutesAndSla(
                from = fromPincode,
                to = toOrgUnit.unitAddress.pincode
            ) ?: throw RouteUnavailableException(
                "The seller *${sellerOrgExtended.orgAccount.displayName} " +
                    "($fromPincode)* will not be able to ship to pincode " +
                    "*${toOrgUnit.unitAddress.pincode}*"
            )
            validateShippingProfile(
                order.orderId,
                shippingProfile,
                routeSla.route,
                paymentModes,
                toOrgUnitId,
                sellerOrgUnitId,
                toOrgUnit.unitAddress.pincode
            ).await()

            val sellerOrderEta = OrderModelHelper.fetchSellerOrderEtaFromPromiseInfo(order, promiseInfo)
            val platform = order.derivePlatform(SellingPlatform.UDAAN_MARKETPLACE)

            logger.info("Order id : ${order.orderId} Promise Info $promiseInfo and seller order eta $sellerOrderEta")

            val orderLineDeliverySlotValidationLines = order.orderLineList.map {
                OrderLineDeliverySlotValidationLine(
                    salesUnitId = it.salesUnitId,
                    listingId = it.listingId,
                    tier = if (it.miscInfo.ffInventoryTier.isNullOrEmpty()) {
                        null
                    } else {
                        PromisedInventoryTierType.valueOf(it.miscInfo.ffInventoryTier)
                    }
                )
            }
            logger.info(
                "Order id : ${order.orderId} " +
                        "orderLineDeliverySlotValidationLines=$orderLineDeliverySlotValidationLines"
            )

            // Final delivery slot to be used from here on
            val finalDeliverySlotId: String? = validateSelectedDeliverySlot(
                fulfillmentCenter = fulfillmentCenter,
                shippingProfile = shippingProfile,
                sellerId = order.sellerId,
                toOrgUnit = toOrgUnit,
                selectedDeliverySlotId = selectedDeliverySlotId,
                selectedItemLevelDeliverySlot = selectedItemLevelDeliverySlot,
                orderId = order.orderId,
                platform = platform,
                orderLineDeliverySlotValidationLines = orderLineDeliverySlotValidationLines
            )

            val pointSla = pointSlaOverride ?: findPointSla(
                fulfillmentCenter = fulfillmentCenter,
                shippingProfile = shippingProfile,
                sellerId = order.sellerId,
                toOrgUnit = toOrgUnit,
                selectedDeliverySlotId = finalDeliverySlotId,
                orderId = order.orderId,
                platform = platform
            ).await()

            logger.info("Order id : ${order.orderId} pointSlaOverride : $pointSlaOverride pointSla : $pointSla")

            val categoryGroupId = (if (order.extraData.categoryGroupId.isNullOrEmpty() && order.orderLineCount > 0) {
                categoryConfigHelper.getCategoryGroup(order.orderLineList.first().listingId, null, sellerOrg)
            } else {
                order.extraData.categoryGroupId
            }) ?: ""

			val comboIds = order.orderLineList.filter { it.isCombo() }.map { it.properties.comboId }.toSet().toList()
			val comboDetails = OrderModelHelper.fetchComboDetails(comboIds, catalogCombosClient)
            val priceContext = fetchPricingContext(
                order = order,
                buyerOrg = buyerOrg,
                toOrgUnitId = toOrgUnitId,
                listingsMap = listingMap
            )
            val deliveryEstimates = if (CategoryTreeV2.marioCategoryGroups.contains(order.extraData.categoryGroupId)) {
                val deliveryEstimateRequests = order.orderLineList.map { orderLine ->
                    DeliveryEstimateRequest(
                        sourceOrgUnitId = sourceOrgUnit.orgUnitId,
                        listingId = orderLine.listingId,
                        destinationOrgUnitId = toOrgUnitId,
                        fulfilmentCenter = fulfillmentCenter,
                    )
                }
                logisticsConsoleServiceClient.getPromiseInfoDeliveryEstimateBulk(
                    DeliveryEstimateBulkRequest(reqs = deliveryEstimateRequests)
                ).executeAwait().responses
            } else emptyList()
            logger.info("Delivery estimates for ${order.orderId} are $deliveryEstimates")
			val orderLines = order.orderLineList.map { orderLine ->
                validateOrderLine(orderLine, listingMap)
                updateOrderLine(
                    shippingProfile,
                    routeSla.route,
                    routeSla.laneSla,
                    orderLine,
                    paymentModes,
                    pointSla,
                    getPharmaAttributes(
                        shippingProfile,
                        orderLine,
                        listingMap.getOrDefault(orderLine.listingId, null),
						comboDetails,
                        priceContext,
                        buyerOrg,
                        toOrgUnitId,
                    ),
                    listingMap[orderLine.properties.listingId]?.salesUnitList?.firstOrNull { x -> x.salesUnitId == orderLine.properties.salesUnitId },
                    categoryGroupId,
                    deliveryEstimateResponse = deliveryEstimates.find { it.listingId == orderLine.listingId }
                )
            }
            orderLinesToAdd.map { orderLine ->
                validateOrderLine(orderLine, listingMap)
            }

            var sellerOrder = order.toBuilder()
                .apply {
                    this.sellerId = order.sellerId
                    this.buyerId = buyerOrgId
                    this.totalOrderSpPaise = orderLines.map { it.orderLineSpPaise }.sum()
                    this.addAllFulfillmentLine(listOf(fulfillmentLine))
                    if (!isReplacementOrder) this.clearTaxLine().addAllTaxLine(taxLine)
                    this.clearOrderLine()
                    this.extraData = order.extraData.toBuilder().setCategoryGroupId(categoryGroupId).build()
                    this.addAllOrderLine(orderLines)
                    if (order.extraData.platformId != null) {
                        this.setPlatformId(order.extraData.platformId)
                    }
                }.build()

            /** TODO - use the prepaymentAmountInPaisa field from [OrderV2.PlaceOrderV2] */
            val prepaymentData = preCalculatedPrepaymentData?.let {
                it.copy(prepaymentAmountPaise = it.calculatePrepaymentAmount(order.orderValueInPaisa()))
            } ?: riskDetector.prepaymentRequired(
                sellerOrder = sellerOrder,
                listingMap = listingMap,
                buyerSelectedPaymentModes = paymentModes,
                deliveryCharges = 0L,
                multipleOrders = emptyList(),
            ).await()
            logger.info("${sellerOrder.orderId} prepayment data: $prepaymentData")
            if (prepaymentData.isPrepaymentRequired &&
                paymentModes.first() == PaymentGatewayV1.PaymentInstrument.COD ) {
                logger.error("Prepayment data has pre-payment option and selected payment mode is COD")
                throw PaymentModeMisMatchException("Order is allowed to be placed only on ONLINE")
            }
            // Cancellation fee must be collected on all orders placed together
            // If order total is not matching with original total, calculate % of cancellation fee
            val cancellationFee = if (prepaymentData.cancellationFeeInPaise > 0) {
                logger.info("Calculating cancellation fee for ${sellerOrder.orderId}, " +
                        "fee: ${prepaymentData.cancellationFeeInPaise}")
                if (sellerOrder.orderValueInPaisa() < originalOrderValue) {
                    val weight = sellerOrder.orderValueInPaisa() / originalOrderValue.toDouble()
                    (prepaymentData.cancellationFeeInPaise * weight).roundToLong()
                } else {
                    prepaymentData.cancellationFeeInPaise
                }
            } else 0L
            logger.info("Applying calculating cancellation fee for ${sellerOrder.orderId}, fee: $cancellationFee")

            val instruct = if (enableSellerMessageOnOrderNotes) {
                instruction + (redisClientPool.asyncCommands.let {
                    it.hget("seller_message", order.sellerId)
                }.toCompletableFuture().await() ?: "")
            } else {
                instruction
            }

            val prepaymentDeliveryCharges = findPrepaidDeliveryCharges(
                sellerOrder = sellerOrder,
                sellerOrg = sellerOrg,
                paymentInstrument = paymentModes,
                profile = shippingProfile,
                toOrgUnitId = toOrgUnitId,
                route = routeSla.route,
            )

            val orderLineLevies = prepareAllLevies(
                sellerOrder = sellerOrder,
                sellerGstin = fromOrgUnit.gstin,
                buyerGstin = toOrgUnit.gstin,
                buyerPincode = toOrgUnit.unitAddress?.pincode,
                orderLines = sellerOrder.orderLineList,
                sellerPincode = fromOrgUnit.unitAddress?.pincode,
                listingMap = listingMap
            )

            // Representing complete 100% pre-payment
            val isAdvancedPayment = paymentModes.firstOrNull() == PaymentGatewayV1.PaymentInstrument.ONLINE &&
                    selectedOnlinePaymentType == SellerOrderInternals.ExtraData.SelectedOnlinePaymentType.Advance
            val extraData = SellerOrderInternals.ExtraData.newBuilder()
                .apply {
                    this.selectedPaymentMethod = paymentModes.first()
                    this.selectedOnlinePaymentType = selectedOnlinePaymentType
                    this.needGSTIN = needgstin
                    this.buyerRemarks = instruct
                    this.isClosed = sellerOrder.extraData.isClosed

                    if (isAdvancedPayment) {
                        val orderWithLevies = sellerOrder
                            .toBuilder()
                            .clearOrderLineLevy()
                            .addAllOrderLineLevy(orderLineLevies.toOMSOrderLineLevies())
                            .build()
                        val prepaymentAmountInPaisa = orderWithLevies.orderValueInPaisa()
                        this.prepaymentAmountInPaisa = prepaymentAmountInPaisa
                        this.holdOrderPolicy = prepaymentData.appliedPolicy
                    } else if (prepaymentData.isPrepaymentRequired) {
                        val prepaymentAmountInPaisa = prepaymentData.prepaymentAmountPaise
                        this.prepaymentAmountInPaisa = prepaymentAmountInPaisa
                        this.holdOrderPolicy = prepaymentData.appliedPolicy
                    } else if (this.selectedPaymentMethod == PaymentGatewayV1.PaymentInstrument.ONLINE) {
                        // This scenario comes when user was asked for prepayment during draft but while confirmation neither he chose to pay full advance
                        // nor he was eligible for mandatory token prepayment. We'll convert such orders to COD since they are no longer eligible for
                        // online payment
                        this.prepaymentAmountInPaisa = 0L
                        this.selectedPaymentMethod = PaymentGatewayV1.PaymentInstrument.COD
                        this.selectedOnlinePaymentType = SellerOrderInternals.ExtraData.SelectedOnlinePaymentType.None
                    }
                    this.draftOrderCreatedAt = order.createdAt
                    this.splitFromOrder = order.extraData.splitFromOrder
                    this.pointSla = pointSla
                    finalDeliverySlotId?.let {
                        this.selectedDeliverySlotId = it
                    }
                    this.billToOrgUnitId = billToOrgUnitId
                    this.orderGroupId = sellerOrder.extraData.orderGroupId
                    this.isTakenOnPromisedTier = sellerOrder.extraData.isTakenOnPromisedTier
                    this.platformId = sellerOrder.extraData.platformId
                    this.categoryGroupId = sellerOrder.extraData.categoryGroupId
                    this.addAllPromotionData(sellerOrder.extraData.promotionDataList)
                    this.superclubSubscriptionId = sellerOrder.extraData.superclubSubscriptionId
                    this.prepaymentDeliveryChargesInPaisa = prepaymentDeliveryCharges.await()
                    buyerOrg.getQualifierTags()?.let { tags ->
                        this.addAllBuyerQualifierTags(tags)
                    }
                    this.freshKamOriginalOrderId = sellerOrder.extraData.freshKamOriginalOrderId
                    this.isB2B2COrder = listingMap.isNotEmpty() && listingMap.all { it.value.config.isB2B2CListing }
                    this.billFromOrgUnitId = fromOrgUnitId
                    this.cancellationFeeInPaise = cancellationFee
                    this.minimumCartValue = sellerOrder.extraData.minimumCartValue
                    this.creditTenurePeriod = sellerOrder.extraData.creditTenurePeriod
                    this.creditLineId = sellerOrder.extraData.creditLineId
                    this.flexiblePayOnDelivery = sellerOrder.extraData.flexiblePayOnDelivery
                }.build()


            sellerOrder = sellerOrder.toBuilder()
                .apply {
                    this.orderStatus = when {
                        extraData.prepaymentAmountInPaisa > 0L -> StatesV1.SellerOrderState.SELLER_ORDER_PAYMENT_OPEN
                        else -> StatesV1.SellerOrderState.SELLER_ORDER_PAYMENT_RECEIVED
                    }
                    this.extraData = extraData
                }.build()

            if (isReplacementOrder) {
                sellerOrder = sellerOrder.toBuilder().apply {
                    this.totalOrderSpPaise = 0
                    val taxOfReplacement = this.taxLineList.map { it.toBuilder().setTaxAmountPaise(0).build() }
                    this.clearTaxLine()
                    addAllTaxLine(taxOfReplacement)
                }.build()
            }

            logger.info("Just before placing order - ${sellerOrder.toString().replace('\n', ' ')}")

            // profile order based on order details
            sellerOrder = profileOrder( order = sellerOrder, shipToOrgUnitId = toOrgUnitId, listingMap = listingMap)

            val promoLines = sellerOrder.orderLineList.flatMap { orderLine ->
                orderLine.properties.promotionDataList.map {
                    it.toPromoLine(orderLine)
                }
            }
            logger.info("Prepared promo lines: $promoLines")

            val orderLinesTrimmedPromos = sellerOrder.orderLineList.map { it.trimPromos() }

            return PlaceOrderPlan(
                sellerOrder = sellerOrder.toBuilder()
                    .clearOrderLine().addAllOrderLine(orderLinesTrimmedPromos)
                    .clearOrderLineLevy().addAllOrderLineLevy(orderLineLevies.toOMSOrderLineLevies())
                    .build(),
                hasPrepayment = prepaymentData.isPrepaymentRequired || extraData.prepaymentAmountInPaisa > 0L,
                discardedOrderLines = orderLinesToDiscard,
                newOrderLines = orderLinesToAdd.map { it.trimPromos() },
                orderLineLevies = orderLineLevies,
                sellerOrderEta = sellerOrderEta,
                listingMap = listingMap,
                paymentModes = paymentModes,
                isAdvancePayment = isAdvancedPayment,
                promoLines = promoLines,
                rewardLine = rewardLine,
            )
        } catch (e: Exception) {
            throw e
        }
    }

    fun placeClosedDraftOrder(order: ModelV1.SellerOrder,
                              toOrgUnitId: String, // its already captured use it from order
                              userId: String,
                              paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
                              instruction: String? = "",
                              needgstin: Boolean = true,
                              selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
                              billToOrgUnitId: String = "",
                              promiseInfo: PromiseInfoResponse? = null,
                              conn: Handle
    ): CompletableFuture<ModelV1.SellerOrder>  {
        return TelemetryScope.future(Dispatchers.IO) {
            val sellerOrderQDao = conn.attach(SellerOrderDao.Query::class.java)
            val isNewOrderFut = TelemetryScope.future(Dispatchers.IO) {
                sellerOrderQDao.lookupOrder(order.orderId) == null
            }

            var buyerOrgUnitId = toOrgUnitId
            val buyerOrgId = order.buyerId
            val buyerOrgFuture = orgRepository.getOrg(buyerOrgId)
            val sellerOrgFuture = orgRepository.getOrgExtended(order.sellerId)

            val sellerOrg = sellerOrgFuture.await()
            val buyerOrg = buyerOrgFuture.await()

            val fulfilmentLines = orderFormDAL.getFulfilmentLine(order.orderId, conn)
            if (fulfilmentLines.isEmpty()) {
                throw AllocatedOrderException("Fulfilment for the reservation is not ready")
            }
            val fulfilmentCenter = fulfilmentLines.first().fulfillmentCenter

            if (fulfilmentCenter == StatesV1.FulfillmentCenter.FFC_SELLER) {
                validateSellerAvailability(sellerOrg)
            }

            buyerOrgUnitId = if (fulfilmentLines.first().shiptoAddressId.isNotEmpty()) fulfilmentLines.first().shiptoAddressId else toOrgUnitId
            val reservedSellerOrgUnit = fulfilmentLines.first().shipfromAddressId

            val listingMap: Map<String, ModelV2.TradeListing> = CatalogBatchCall.getTradeListings(
                    order.orderLineList
                            .filter { it.itemType == ModelV1.ItemType.LISTING }
                            .map { ol -> ol.properties.listingId }
                            .distinct(), catalogProvider).await()
                    .filter { l ->
                        l.status == ModelV2.TradeListing.Status.ACTIVE
                    }
                    .associateBy { k ->
                        k.listingId
                    }


            logger.info("placeDraftOrder buyerOrgId: {}", buyerOrgId)

            val taxLine = order.orderLineList
                    .filter { it.itemType == ModelV1.ItemType.LISTING }
                    .flatMap {
                        taxationBizLogic.getGoodsTaxDetails(it, listingMap[it.properties.listingId]
                                ?: throw ListingNotFoundException("Listing with id: ${it.properties.listingId} not found"))
                    }


            val orderValueInPaisa = order.orderLineList.map { it.orderLineSpPaise }.sum() + taxLine.sumBy { it.taxAmountPaise.toInt() }

            //check if amount is exceeding the payment limit
            val paymentLimit = checkPaymentLimit(paymentModes[0], orderValueInPaisa, buyerOrgId)
            if (paymentLimit > 0) {
                val msg = "For " + paymentModes[0].name + " the maximum limit for order is " + paymentLimit.paiseToRsStr() + "."
                throw PaymentValueOutOfBoundsException(msg)
            }

            val toOrgUnit = buyerOrg.orgUnitsMap[buyerOrgUnitId ?: toOrgUnitId]!!

            if (toOrgUnit.unitAddress.addressLine1.isNullOrBlank()) {
                logger.error("Found org unit without sufficient info $userId ${toOrgUnit.orgUnitId}")
                throw InvalidAddressException("Address does not have any location information. Please select a valid address")
            }

            val sellerOrgExtended = sellerOrgFuture.await()
            val sellerOrgUnitId = if (reservedSellerOrgUnit.isEmpty()) sellerOrgExtended.orgAccount.headOfficeOrgUnitRef else reservedSellerOrgUnit
            val fromOrgUnit = sellerOrgExtended.orgAccount.orgUnitsMap[sellerOrgUnitId]
                    ?: throw Exception("Seller ${order.sellerId} org unit not found - $sellerOrgUnitId for buyer - $buyerOrgId")

            validateSellerGSTIN(fromOrgUnit, taxLine)

            Telemetry.trackEvent("TEMP_EVENT_SELLER_ORDER_PLACEMENT_HO",
                    mapOf(
                            DIM_FF_ORDER_PLACEMENT_ORDER_ID to (order.orderId ?: ""),
                            "OrgUnitId" to fromOrgUnit.orgUnitId!!,
                            "OrigIdIfAny" to (sellerOrgUnitId ?: ""),
                            "FF_From" to sellerOrgUnitId
                    ),
                    mapOf()
            )

            if (isInterCitySeller(sellerOrg) && !isEnterpriseBuyer(buyerOrg)) {
                val categoryTree = TelemetryScope.future(Dispatchers.IO) {
                    shippingProfileSelector.getCategoryTree(order.orderLineList, sellerOrg)
                }.await()
                validateBlockedSeller(sellerOrg, fulfilmentCenter != StatesV1.FulfillmentCenter.FFC_SELLER)
                validateBlockedServiceability(buyerOrg, toOrgUnit, sellerOrg, categoryTree).await()
            }

            val route = routeDal.getRoutes(fromOrgUnit.unitAddress.pincode, toOrgUnit.unitAddress.pincode)
                        ?: throw RouteUnavailableException("The seller " +
                                "*${sellerOrgExtended.orgAccount.displayName} (${fromOrgUnit.unitAddress.pincode})* " +
                                "will not be able to ship to pincode *${toOrgUnit.unitAddress.pincode}*")

            val shippingProfile = logisticsProvider.getShippingProfile(
                    order.orderLineList.extractListingIds(),
                    sellerOrgExtended.orgAccount.orgId,
                    buyerOrg.orgId).shippingChargeProfile
            if (shippingProfile.allowOnlyProfileCheck()) {
                //For those shipping profiles that allow only profile checks, we can check for availability of service providers pre-order placement,
                //so as to remove ODA possibility
                val (providerAvailable, logisticServiceDeniedReason, message) = TelemetryScope.future(Dispatchers.IO) {
                    checkForServiceProviders(
                        order.orderId, route, paymentModes, shippingProfile,
                        toOrgUnitId, sellerOrgUnitId, toOrgUnit.unitAddress.pincode)
                }.await()

                if (!providerAvailable) throw ProviderUnavailableException(message ?: "No provider to deliver items")
            }

            val slaDetail = routeDal.getLaneInfoFor(route.zone)

            val sellerOrderEta = OrderModelHelper.fetchSellerOrderEtaFromPromiseInfo(order, promiseInfo)

            logger.info("Order id : ${order.orderId}, " +
                    "[Allocation]Promise Info $promiseInfo and seller order eta $sellerOrderEta")

            var pointSla: Long = 0
            if ((shippingProfile == ShippingChargeProfile.FRESH) || (shippingProfile == ShippingChargeProfile.PHARMA) ||
                    (shippingProfile.allowUdFulfillment() && shippingProfile == ShippingChargeProfile.FMCG)) {
                // we will fetch the exact pointSla from the API and assign it in metadata
                val buyerPincode = toOrgUnit.unitAddress.pincode
                val sellerOrgId = order.sellerId
                val isUdaanFulfilled = fulfilmentCenter == StatesV1.FulfillmentCenter.FFC_UDWH

                // we will make the API call here
                val isFpJit = (fulfilmentCenter == StatesV1.FulfillmentCenter.FFC_UDFPJIT)
                val pointSlaResponse = promiseServiceClient.getCutoffAndDeliveryDay(
                    buyerPincode = buyerPincode,
                    sellerOrgId = sellerOrgId,
                    isUdaanFulfilled = isUdaanFulfilled,
                    isFpJit = isFpJit
                ).executeAwait()
                pointSla = pointSlaResponse.deliveryInUnix
            }

            val categoryGroupId = (if (order.extraData.categoryGroupId.isNullOrEmpty() && order.orderLineCount > 0) {
                categoryConfigHelper.getCategoryGroup(order.orderLineList.first().listingId, null, sellerOrg)
            } else {
                order.extraData.categoryGroupId
            }) ?: ""

			val comboIds = order.orderLineList.filter { it.isCombo() }.map { it.properties.comboId }.toSet().toList()
			val comboDetails = OrderModelHelper.fetchComboDetails(comboIds, catalogCombosClient)
            val priceContext = fetchPricingContext(
                order = order,
                buyerOrg = buyerOrg,
                toOrgUnitId = toOrgUnitId,
                listingsMap = listingMap
            )
            val orderLines = order.orderLineList.map { orderLine ->
                TelemetryScope.future(Dispatchers.IO) {
					updateOrderLine(
						shippingProfile,
						route,
						slaDetail,
						orderLine,
						paymentModes,
						pointSla,
						getPharmaAttributes(
							shippingProfile,
							orderLine,
							listingMap.getOrDefault(orderLine.listingId, null),
							comboDetails,
                            priceContext,
                            buyerOrg,
                            toOrgUnitId,
						),
						listingMap[orderLine.properties.listingId]?.salesUnitList?.firstOrNull { x -> x.salesUnitId == orderLine.properties.salesUnitId },
						categoryGroupId,
                        applyScheme = false,
					)
                }
            }.map {
                it.await()
            }

            var sellerOrder = order.toBuilder()
                    .apply {
                        this.sellerId = order.sellerId
                        this.buyerId = buyerOrgId
                        this.totalOrderSpPaise = orderLines.map { it.orderLineSpPaise }.sum()
                        this.clearTaxLine()
                        this.addAllTaxLine(taxLine)
                        this.clearOrderLine()
                        this.addAllOrderLine(orderLines)
                        this.extraData = order.extraData.toBuilder().setCategoryGroupId(categoryGroupId).build()
                    }.build()

            /** TODO - use the prepaymentAmountInPaisa field from [OrderV2.PlaceOrderV2] */
            val prepaymentDataFuture = riskDetector.prepaymentRequired(
                sellerOrder = sellerOrder,
                listingMap = listingMap,
                buyerSelectedPaymentModes = paymentModes,
                deliveryCharges = 0L,
                multipleOrders = emptyList(),
            )

            val prepaymentData = prepaymentDataFuture.await()

            val instruct = if (OrderFormBizLogic.enableSellerMessageOnOrderNotes) {
                instruction + (redisClientPool.asyncCommands.let {
                    it.hget("seller_message", order.sellerId)
                }.toCompletableFuture().await() ?: "")
            } else {
                instruction
            }

            val blockIds = TelemetryScope.future(Dispatchers.IO) {
                orderPaymentHandler.block(sellerOrder, paymentModes,
                        sellerOrder.orderValueInPaisa(), isPrepaidOnlyOrder(listingMap), conn)
            }.await()

            val prepaymentDeliveryCharges = findPrepaidDeliveryCharges(
                    sellerOrder = sellerOrder,
                    sellerOrg = sellerOrg,
                    paymentInstrument = paymentModes,
                    profile = shippingProfile,
                    toOrgUnitId = toOrgUnitId,
                    route = route,
            )

            val orderLineLevies = prepareAllLevies(
                sellerOrder = sellerOrder,
                sellerGstin = fromOrgUnit.gstin,
                buyerGstin = toOrgUnit.gstin,
                buyerPincode = toOrgUnit.unitAddress?.pincode,
                orderLines = sellerOrder.orderLineList,
                sellerPincode = fromOrgUnit.unitAddress?.pincode,
                listingMap = listingMap
            )

            val extraData = sellerOrder.extraData.toBuilder().apply {
                this.selectedPaymentMethod = paymentModes.first()
                this.needGSTIN = needgstin
                this.buyerRemarks = instruct

                if (blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT].isNullOrEmpty().not())
                    this.creditBlockId = blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT]

                if (blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER].isNullOrEmpty().not())
                    this.creditBlockId = blockIds[PaymentGatewayV1.PaymentInstrument.CREDIT_PAY_LATER]

                if (this.selectedPaymentMethod == PaymentGatewayV1.PaymentInstrument.ONLINE &&
                        selectedOnlinePaymentType == SellerOrderInternals.ExtraData.SelectedOnlinePaymentType.Advance) {
                    val orderWithLevies = sellerOrder
                        .toBuilder()
                        .clearOrderLineLevy()
                        .addAllOrderLineLevy(orderLineLevies.toOMSOrderLineLevies())
                        .build()
                    val prepaymentAmountInPaisa = orderWithLevies.orderValueInPaisa()
                    this.prepaymentAmountInPaisa = prepaymentAmountInPaisa
                    this.holdOrderPolicy = prepaymentData.appliedPolicy
                }
                else if (prepaymentData.isPrepaymentRequired) {
                    val prepaymentAmountInPaisa = prepaymentData.prepaymentAmountPaise
                    this.prepaymentAmountInPaisa = prepaymentAmountInPaisa
                    this.holdOrderPolicy = prepaymentData.appliedPolicy
                }
                this.draftOrderCreatedAt = order.createdAt
                this.splitFromOrder = order.extraData.splitFromOrder
                this.pointSla = pointSla
                selectedDeliverySlotId?.let {
                    this.selectedDeliverySlotId = it
                }
                this.selectedOnlinePaymentType = selectedOnlinePaymentType
                this.billToOrgUnitId = billToOrgUnitId
                this.categoryGroupId = sellerOrder.extraData.categoryGroupId
                this.prepaymentDeliveryChargesInPaisa = prepaymentDeliveryCharges.await()
            }.build()

            sellerOrder = sellerOrder.toBuilder()
                    .apply {
                        this.orderStatus = when {
                            prepaymentData.isPrepaymentRequired -> {
                                StatesV1.SellerOrderState.SELLER_ORDER_PAYMENT_OPEN
                            }
                            prepaymentData.enableAdvancePayment
                                    && paymentModes.firstOrNull() == PaymentGatewayV1.PaymentInstrument.ONLINE -> {
                                StatesV1.SellerOrderState.SELLER_ORDER_PAYMENT_OPEN
                            }
                            else -> StatesV1.SellerOrderState.SELLER_ORDER_PAYMENT_RECEIVED
                        }
                        this.extraData = extraData
                    }.build()

            logger.info("Just before placing order - ${sellerOrder.toString().replace('\n',' ')}")

            val isNewOrder = isNewOrderFut.await()
            if(isNewOrder) {
                Telemetry.trackEvent("Order-form-new-order", mapOf("orderId" to order.orderId), mapOf())
            }

            logger.info("[placeClosedDraftOrder] For ${sellerOrder.orderId} isNewOrder is $isNewOrder , " +
                    "prepaymentRequired is ${prepaymentData.isPrepaymentRequired} and levies are $orderLineLevies")
            TelemetryScope.future(Dispatchers.IO) {
                orderFormDAL.placeClosedOrderForm(sellerOrder, prepaymentData.isPrepaymentRequired, orderLineLevies, sellerOrderEta, conn)
            }.await()

            sellerOrder
        }
    }
}

private data class CodLimit(
    val buyers: List<String>,
    val limit: Long
)

private data class CodLimitConfig(
    val codLimitList: List<CodLimit>,
)

private val OrgV1.OrgAccountExtendedResponse.sellerOooSettings: LongRange?
    get() {
        val startTime = sellingConditions.sellerOooStartTimestamp
        val endTime = sellingConditions.sellerOooEndTimestamp
        return if (startTime in 1 until endTime) {
            startTime..endTime
        } else {
            null
        }
    }

data class PlaceOrderReq(
    val order: ModelV1.SellerOrder,
    val toOrgUnitId: String,
    val paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
    val fromOrgUnitId: String? = null,
    val instruction: String? = "",
    val needgstin: Boolean = true,
    val isReplacementOrder: Boolean = false,
    val fulfillmentCenter: StatesV1.FulfillmentCenter = StatesV1.FulfillmentCenter.FFC_SELLER,
    val orderLinesToDiscard: List<ModelV1.OrderLine> = emptyList(),
    val orderLinesToAdd: List<ModelV1.OrderLine> = emptyList(),
    val selectedDeliverySlotId: String?,
    val selectedItemLevelDeliverySlot: Map<String, String>?,
    val selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
    val billToOrgUnitId: String = "",
    val isInternalFulfilmentOrder: Boolean = false,
    val promiseInfo: PromiseInfoResponse? = null,
    val pointSlaOverride: Long? = null,
    val shipFromOrgUnit: com.udaan.proto.models.ModelV1.OrgUnit? = null,
)
