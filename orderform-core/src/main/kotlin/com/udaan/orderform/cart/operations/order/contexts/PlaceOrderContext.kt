package com.udaan.orderform.cart.operations.order.contexts

import com.udaan.catalog.models.ModelV2
import com.udaan.logistics.models.vsl.ShippingProfileResponse
import com.udaan.order_mgt.events.model.PromiseInfoResponse
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.orderform.cart.common.models.OrderDeliveryChargeData
import com.udaan.orderform.cart.common.models.OrderLineDeliveryCharge
import com.udaan.orderform.cart.common.models.PlaceOrderPlan
import com.udaan.orderform.cart.common.toOMSOrderLineLevies
import com.udaan.orderform.cart.context.OrderContext
import com.udaan.orderform.cart.context.OrderExtractor
import com.udaan.orderform.cart.db.models.ReservationInfo
import com.udaan.orderform.cart.models.dto.AdditionalData
import com.udaan.orderform.cart.models.dto.BaseCheckoutReq
import com.udaan.orderform.models.RewardLine
import com.udaan.orderform.service.business.SplitOrderResponse
import com.udaan.orderservice.models.OrderDeliveryCharge

class PlaceOrderContext<R : BaseCheckoutReq>(
    var request: R,
    val orderIds: List<String>,
    private val sellerOrderContextsMap: Map<String, OrderContext>,
    val shippingProfiles: Map<String, ShippingProfileResponse>? = null,
    private val extractor: OrderExtractor,
) : BaseCheckoutContext {
    private val orderContextsMap = mutableMapOf<String, OrderContext>().also {
        it.putAll(sellerOrderContextsMap)
    }
    private val reservationsInfo: MutableMap<String, List<ReservationInfo>> = mutableMapOf()
    private var placedOrders: List<ModelV1.SellerOrder> = emptyList()
    private val originalOrderContextsMap: MutableMap<String, OrderContext> = mutableMapOf()
    private val promiseInfoMap: MutableMap<String, PromiseInfoResponse> = mutableMapOf()
    private var placeOrderPlans: List<PlaceOrderPlan> = mutableListOf()
    private var transactionContext: TransactionContext? = null
    private var splitOrderResponse: List<SplitOrderResponse>? = null
    private var rewardLines: List<RewardLine>? = null
    private var orderDeliveryCharge: OrderDeliveryCharge? = null
    private var orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>? = emptyMap()
    private var orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>? = emptyMap()
    private var itemDeliverySlotMap: Map<String, String>? = emptyMap()

    fun updateRequest(request: R) {
        this.request = request
    }

    override suspend fun getOrderContexts(): Map<String, OrderContext> {
        return orderContextsMap
    }

    override suspend fun setOrderContexts(orderContextsMap: Map<String, OrderContext>) {
        this.orderContextsMap.putAll(orderContextsMap)
    }

    internal fun addReservationsInfo(orderId: String, reservationsInfo: List<ReservationInfo>) {
        this.reservationsInfo[orderId] = reservationsInfo
    }

    internal fun getReservationsInfo(orderId: String): List<ReservationInfo> = reservationsInfo[orderId] ?: emptyList()

    internal fun getReservationsInfoMap(): Map<String, List<ReservationInfo>> = reservationsInfo

    internal fun setPlacedOrders(placedOrders: List<ModelV1.SellerOrder>) {
        this.placedOrders = placedOrders
    }

    override fun getUserRequest(): R = request

    override fun useRewardCoins(): Boolean = request.useRewards()

    override fun getPaymentModes(): List<PaymentGatewayV1.PaymentInstrument> = request.getSelectedPaymentModes()

    override fun getBuyerId(): String = request.getBuyerId()

    override fun getToOrgUnitId(): String = request.getDeliveryOrgUnitId()

    override suspend fun getBaseOrders(): List<ModelV1.SellerOrder> =
        originalOrderContextsMap.values.mapNotNull { it.getOrder() }

    override fun getPlacedOrders(): List<ModelV1.SellerOrder> = this.placedOrders

    override fun setOriginalOrderContexts(orderContextsMap: Map<String, OrderContext>) {
        this.originalOrderContextsMap.clear()
        this.originalOrderContextsMap.putAll(orderContextsMap)
    }

    override fun getOriginalOrdersContext() = originalOrderContextsMap

    override fun addPromiseInfo(orderId: String, promiseInfoResponse: PromiseInfoResponse) {
        this.promiseInfoMap[orderId] = promiseInfoResponse
    }

    override suspend fun getBuyerOrg(): com.udaan.proto.models.ModelV1.OrgAccount? =
        sellerOrderContextsMap.values.firstOrNull()?.getBuyerOrg()?.orgAccount

    override suspend fun getListingsMap(): Map<String, ModelV2.TradeListing> = sellerOrderContextsMap.values.map {
        it.getListingsMap()
    }.flatMap {
        it.asSequence()
    }.associateBy({ it.key }, { it.value })

    fun getPromiseInfo(orderId: String): PromiseInfoResponse? = this.promiseInfoMap[orderId]

    fun getPromiseInfoMap(): Map<String, PromiseInfoResponse> = this.promiseInfoMap

    fun setOrderPlans(orderPlans: List<PlaceOrderPlan>) {
        this.placeOrderPlans = orderPlans
    }

    override fun getOrderPlans(): List<PlaceOrderPlan> {
        return this.placeOrderPlans
    }

    override fun setOrderDeliveryChargeDataMap(orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>) {
        this.orderDeliveryChargeDataMap = orderDeliveryChargeDataMap
    }

    override fun getOrderDeliveryChargeDataMap(): Map<String, OrderDeliveryChargeData>? {
        return this.orderDeliveryChargeDataMap
    }

    override fun setOrderLineDeliveryChargeDataMap(
        orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>
    ) {
        this.orderLineDeliveryChargeDataMap = orderLineDeliveryChargeDataMap
    }

    override fun getOrderLineDeliveryChargeDataMap(): Map<String, List<OrderLineDeliveryCharge>>? {
        return this.orderLineDeliveryChargeDataMap
    }

    override fun setOrderDeliveryCharge(orderDeliveryCharge: OrderDeliveryCharge) {
        this.orderDeliveryCharge = orderDeliveryCharge
    }

    override fun getOrderDeliveryCharge(): OrderDeliveryCharge? {
        return this.orderDeliveryCharge
    }

    override fun setItemLevelDeliverySlot(itemLevelDeliverySlot: Map<String, String>?) {
        this.itemDeliverySlotMap = itemLevelDeliverySlot
    }

    override fun getItemLevelDeliverySlot(): Map<String, String>? {
        return this.itemDeliverySlotMap
    }

    override fun getAdditionalData(): AdditionalData = request.getReqAdditionalData()

    internal fun setTransactionContext(transactionContext: TransactionContext) {
        this.transactionContext = transactionContext
    }

    internal fun getTransactionContext(): TransactionContext? = transactionContext

    internal fun setSplitOrderResponse(splitOrderResponse: List<SplitOrderResponse>) {
        this.splitOrderResponse = splitOrderResponse
    }

    internal fun getSplitOrderResponse(): List<SplitOrderResponse> {
        return splitOrderResponse ?: emptyList()
    }

    override fun getOrdersFromSplits(): List<ModelV1.SellerOrder> {
        return getSplitOrderResponse().flatMap { splitOrderResponse ->
            (splitOrderResponse.fcOrders?.mapNotNull { it.udFcFulfilledOrder } ?: emptyList()).plus(
                splitOrderResponse.flexOrders?.mapNotNull { it.udFcFulfilledOrder } ?: emptyList()
            ).plus(splitOrderResponse.sellerFulfilledOrder.map {
                it
            })
        }
    }

    override suspend fun getOrdersWithTaxesFromSplits(): List<ModelV1.SellerOrder> {
        return getSplitOrderResponse().flatMap { splitOrderResponse ->
            (splitOrderResponse.fcOrders?.mapNotNull { it.udFcFulfilledOrder?.let { fillInTaxes(it) } }
                ?: emptyList()).plus(
                splitOrderResponse.flexOrders?.mapNotNull { it.udFcFulfilledOrder?.let { fillInTaxes(it) } }
                    ?: emptyList()
            ).plus(splitOrderResponse.sellerFulfilledOrder.map {
                fillInTaxes(it)
            })
        }
    }

    internal fun setRewardLines(rewardLines: List<RewardLine>) {
        this.rewardLines = rewardLines
    }

    override fun getRewardLines(): List<RewardLine> {
        return rewardLines ?: emptyList()
    }

    override fun getCouponIds(): List<String> = request.getReqCouponIds()

    private suspend fun fillInTaxes(order: ModelV1.SellerOrder): ModelV1.SellerOrder {
        val listingsMap = getListingsMap()
        val levies = extractor.fetchLeviesMap(
            order,
            listingsMap
        ).values.flatten().toOMSOrderLineLevies()

        return order.toBuilder()
            .clearOrderLineLevy().addAllOrderLineLevy(levies)
            .build()
    }
}
