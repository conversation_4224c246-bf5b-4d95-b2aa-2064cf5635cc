package com.udaan.orderform.cart.operations.order.contexts

import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.order_mgt.events.model.PromiseInfoResponse
import com.udaan.order_mgt.models.ModelV1.SellerOrder
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.orderform.cart.common.models.OrderDeliveryChargeData
import com.udaan.orderform.cart.common.models.OrderLineDeliveryCharge
import com.udaan.orderform.cart.common.models.PlaceOrderPlan
import com.udaan.orderform.cart.context.BaseOperationContext
import com.udaan.orderform.cart.context.OrderContext
import com.udaan.orderform.cart.models.dto.AdditionalData
import com.udaan.orderform.cart.models.dto.BaseCheckoutReq
import com.udaan.orderform.models.RewardLine
import com.udaan.orderservice.models.OrderDeliveryCharge
import com.udaan.proto.models.ModelV1.OrgAccount

interface BaseCheckoutContext: BaseOperationContext {
    fun getUserRequest(): BaseCheckoutReq

    fun useRewardCoins(): Boolean

    fun getPaymentModes(): List<PaymentGatewayV1.PaymentInstrument>

    fun getBuyerId(): String

    fun getToOrgUnitId(): String

    suspend fun getBaseOrders(): List<SellerOrder>

    fun getPlacedOrders(): List<SellerOrder>

    fun setOriginalOrderContexts(orderContextsMap: Map<String, OrderContext>)

    fun getOriginalOrdersContext(): Map<String, OrderContext>

    fun getOrderPlans(): List<PlaceOrderPlan>

    fun getAdditionalData(): AdditionalData

    fun addPromiseInfo(orderId: String, promiseInfoResponse: PromiseInfoResponse)

    fun getRewardLines(): List<RewardLine>

    fun getCouponIds(): List<String>

    fun getOrdersFromSplits(): List<SellerOrder>

    suspend fun getOrdersWithTaxesFromSplits(): List<SellerOrder>
    suspend fun getBuyerOrg(): OrgAccount?
    suspend fun getListingsMap(): Map<String, TradeListing>
    fun setOrderDeliveryChargeDataMap(orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>)
    fun getOrderDeliveryChargeDataMap(): Map<String, OrderDeliveryChargeData>?
    fun setOrderLineDeliveryChargeDataMap(orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>)
    fun getOrderLineDeliveryChargeDataMap(): Map<String, List<OrderLineDeliveryCharge>>?
    fun setOrderDeliveryCharge(orderDeliveryCharge: OrderDeliveryCharge)
    fun getOrderDeliveryCharge(): OrderDeliveryCharge?
    fun setItemLevelDeliverySlot(itemLevelDeliverySlot: Map<String, String>?)
    fun getItemLevelDeliverySlot() : Map<String, String>?
}
