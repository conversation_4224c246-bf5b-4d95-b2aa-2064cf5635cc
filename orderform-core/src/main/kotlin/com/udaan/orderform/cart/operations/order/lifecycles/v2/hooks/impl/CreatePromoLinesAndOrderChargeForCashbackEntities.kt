package com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.impl

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.right
import com.udaan.common.utils.kotlin.logger
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.service.tiny_utils.Utils
import com.udaan.orderform.cart.hooks.BaseOperationHook
import com.udaan.orderform.cart.models.dto.CheckoutReqDto
import com.udaan.orderform.cart.models.dto.ItemCashback
import com.udaan.orderform.cart.operations.order.contexts.CheckoutContext
import com.udaan.orderform.models.ChargeCategory
import com.udaan.orderform.models.ChargeStatus
import com.udaan.orderform.models.ChargeType
import com.udaan.orderform.models.OrderCharges
import com.udaan.orderform.models.OrderLineCharge
import com.udaan.orderform.models.PromoApplicabilityLevel
import com.udaan.orderform.models.PromoLine
import com.udaan.orderform.models.PromoSource
import com.udaan.orderform.models.PromotionDetails
import kotlin.String
import kotlin.getValue

class CreatePromoLinesAndOrderChargeForCashbackEntities : BaseOperationHook<CheckoutContext<CheckoutReqDto>> {
    companion object {
        private val logger by logger()
    }

    // Treating cashaback promoLines independently from existing promoLines from promotions as they can be different
    // and also be stored in order charge table
    override suspend fun execute(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        logger.info("[CreatePromoLinesPostPlacePlanHook] Updating PlaceOrderPlans with cashback entities")
        val request = context.request
        val currentPlaceOrderPlans = context.getPlaceOrderPlans()
        val sellerOrders = currentPlaceOrderPlans.map { it.sellerOrder }

        val orderToCashbackEntities = createCashbackEntitiesByOrderId(sellerOrders, request)

        if (orderToCashbackEntities.isEmpty()) {
            logger.info("[CreatePromoLinesPostPlacePlanHook] No cashback entities to process for any order.")
            return context.right()
        }

        val updatedPlaceOrderPlans = currentPlaceOrderPlans.map { plan ->
            orderToCashbackEntities[plan.sellerOrder.orderId]?.let { cashbackData ->
                plan.copy(
                    promoLines = plan.promoLines + cashbackData.promoLines,
                    orderCharge = cashbackData.orderCharge,
                    orderLineCharges = cashbackData.orderLineCharges
                )
            } ?: plan // If no cashback data for this order's ID, keep the plan as is
        }

        context.setPlaceOrderPlans(updatedPlaceOrderPlans)

        return context.right()
    }

    private fun createCashbackEntitiesByOrderId(
        placeOrders: List<ModelV1.SellerOrder>,
        request: CheckoutReqDto
    ): Map<String, CashbackEntities> {
        return placeOrders.associate { order ->
            val orderChargeId = "OC${Utils.getSerialId()}"
            val promoLines = mutableListOf<PromoLine>()
            val orderLineCharges = mutableListOf<OrderLineCharge>()

            order.orderLineList.map { orderLine ->
                request.products.find {
                    it.listingId == orderLine.listingId && it.salesUnitId == orderLine.salesUnitId
                }?.cashbacks?.let { itemCashbacks ->
                    promoLines.addAll(preparePromoLines(orderLine, itemCashbacks))
                    orderLineCharges.addAll(createOrderChargeEntityForOrderLine(orderLine, itemCashbacks, orderChargeId))
                }
            }

            val orderCharge = if (orderLineCharges.isNotEmpty()) {
                createOrderChargeEntityForOrder(orderLineCharges, orderChargeId, order.orderId)
            } else {
                null
            }
            order.orderId to CashbackEntities(promoLines, orderCharge, orderLineCharges)
        }.filter { it.value.promoLines.isNotEmpty() || it.value.orderCharge != null }
    }

    private fun preparePromoLines(
        orderLine: ModelV1.OrderLine,
        itemCashbacks: List<ItemCashback>
    ): List<PromoLine> {
        return itemCashbacks.map { itemCashback ->
            val applicabilityLevel = if (itemCashback.isItemLevel) {
                PromoApplicabilityLevel.ORDERLINE_LEVEL
            } else {
                PromoApplicabilityLevel.ORDER_LEVEL
            }
            val source = when (itemCashback.source.name) {
                PromoSource.PROMOTIONS.name -> PromoSource.PROMOTIONS
                else -> throw IllegalArgumentException("Unknown source for cashback - ${itemCashback.source}")
            }
            val promoDetails = PromotionDetails(
                discountBps = itemCashback.discountBps,
                tierIndex = 0,
                rewardClass = itemCashback.rewardClass,
                rewardedOrderLine = emptyList()
            )
            PromoLine(
                id = "PL${Utils.getSerialId()}",
                promoId = itemCashback.identifier,
                orderId = orderLine.sellerOrderId,
                orderLineId = orderLine.orderLineId,
                applicabilityLevel = applicabilityLevel,
                source = source,
                promoDetails = promoDetails,
                currentActive = true
            )
        }
    }

    private fun createOrderChargeEntityForOrder(orderLineCharges: List<OrderLineCharge>, orderChargeId: String, orderId: String): OrderCharges {
        val referenceId = orderLineCharges.first().referenceId
        return OrderCharges(
            orderChargeId = orderChargeId,
            referenceId = referenceId,
            orderId = orderId,
            chargeAmountPaise = orderLineCharges.sumOf { it.chargeAmountPaise },
            chargeBps = null,
            chargeType = ChargeType.DISCOUNT,
            chargeCategory = ChargeCategory.INSTANT_CASHBACK,
            chargeStatus = ChargeStatus.ACTIVE,
            offerDesc = ""
        )
    }

    private fun createOrderChargeEntityForOrderLine(
        orderLine: ModelV1.OrderLine,
        itemCashbacks: List<ItemCashback>,
        orderChargeId: String
    ): List<OrderLineCharge> {
        return itemCashbacks.map { itemCashback ->
            val originalOrderLineAmountinPaise = orderLine.properties.originalPerUnitPaise
            val chargeAmountPaise = orderLine.units.times(originalOrderLineAmountinPaise).times(itemCashback.discountBps).div(10000)
            OrderLineCharge(
                orderLineChargeId = "OLC${Utils.getSerialId()}",
                orderChargeId = orderChargeId,
                referenceId = itemCashback.identifier,
                orderId = orderLine.sellerOrderId,
                orderLineId = orderLine.orderLineId,
                chargeAmountPaise = chargeAmountPaise,
                currentActive = true,
                chargeBps = itemCashback.discountBps,
            )
        }
    }

    private data class CashbackEntities(
        val promoLines: List<PromoLine>,
        val orderCharge: OrderCharges?,
        val orderLineCharges: List<OrderLineCharge>
    )
}
