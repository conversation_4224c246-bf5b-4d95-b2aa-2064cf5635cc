package com.udaan.orderform.cart.common.models

import com.udaan.invoicing.models.UniqueQuantityCode
import com.udaan.orderservice.models.DeliveryChargeEntityType
import com.udaan.orderservice.models.DeliveryChargePayer
import com.udaan.orderservice.models.DeliveryChargeStatus
import com.udaan.proto.models.ModelV1.SellingPlatform

data class OrderDeliveryChargeData(
    val entityId: String,
    val entityType: DeliveryChargeEntityType,
    val chargeStatus: DeliveryChargeStatus,
    val deliveryChargeAmountInPaise: Long,
    val chargePayer: DeliveryChargePayer,
    val platformId: SellingPlatform,
    val orderId: String
)

data class OrderLineDeliveryCharge(
    val orderId: String,
    val orderLineId: String,
    val quantityOrdered: Double,
    val uqc: UniqueQuantityCode,
    val deliveryChargeAmountInPaisa: Long,
    val perUnitDeliveryChargeAmountInPaisa: Long,
    val platformId: SellingPlatform
)
