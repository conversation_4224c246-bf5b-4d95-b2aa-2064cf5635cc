package com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.impl

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.right
import awaitOrNull
import com.google.inject.Inject
import com.udaan.catalog.client.helpers.Category
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.constraint.models.dtos.ItemLevelDeliveryCharge
import com.udaan.constraint.models.dtos.LogChargeEntityType
import com.udaan.dropslot.client.DropslotServiceClient
import com.udaan.invoicing.models.UniqueQuantityCode
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.orderform.cart.common.models.OrderDeliveryChargeData
import com.udaan.orderform.cart.common.models.OrderLineDeliveryCharge
import com.udaan.orderform.cart.hooks.BaseOperationHook
import com.udaan.orderform.cart.models.dto.CheckoutReqDto
import com.udaan.orderform.cart.operations.order.contexts.CheckoutContext
import com.udaan.orderform.common.models.LogCharge
import com.udaan.orderform.common.models.LogChargePayer
import com.udaan.orderform.common.providers.LogChargeProvider
import com.udaan.orderform.common.providers.PreOrderEtaProvider
import com.udaan.orderform.common.providers.toSupplyChainCategory
import com.udaan.orderform.service.sumByLong
import com.udaan.orderservice.models.DeliveryChargeEntityType
import com.udaan.orderservice.models.DeliveryChargePayer
import com.udaan.orderservice.models.DeliveryChargeStatus
import com.udaan.proto.models.ModelV1
import com.udaan.proto.models.ModelV1.SellingPlatform
import kotlinx.coroutines.future.await
import arrow.core.nonEmptyListOf
import arrow.core.left
import com.fasterxml.jackson.databind.ObjectMapper
import com.udaan.config.client.BusinessConfigClient
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.orchestrator.models.v2.SupplyChainCategory
import com.udaan.orderform.cart.db.repo.OrderFormReadRepo
import com.udaan.orderform.common.providers.OrgIdentityProvider
import com.udaan.orderform.constants.CategoryConstants.FMCG_CATEGORY
import com.udaan.orderform.constants.CategoryConstants.FOOD_AND_FMCG_CATEGORY
import com.udaan.orderform.constants.CategoryConstants.FRESH_CATEGORY
import com.udaan.orderform.constants.CategoryConstants.MEAT_CATEGORY
import com.udaan.orderform.service.business.orderValueInPaisa
import java.time.temporal.ChronoUnit

class DeliveryChargeFetchHook @Inject constructor(
        private val logChargeProvider: LogChargeProvider,
        private val verticalCache: VerticalCache,
        private val preOrderEtaProvider: PreOrderEtaProvider,
        private val dropslotServiceClient: DropslotServiceClient,
        private val categoryConfigHelper: CategoryConfigHelper,
        private val businessConfigClient: BusinessConfigClient,
        private val orderFormReadRepo: OrderFormReadRepo,
        private val orgIdentityProvider: OrgIdentityProvider,
        private val objectMapper: ObjectMapper
) : BaseOperationHook<CheckoutContext<CheckoutReqDto>> {
    companion object {
        private val logger by logger()
        private val DEFAULT_LOG_CHARGE = LogCharge(
            deliveryChargesInPaise = 0L,
            logChargeEntityType = LogChargeEntityType.DEFAULT,
            itemLevelLogCharge = emptyList(),
            chargePayer = LogChargePayer.BUYER,
            isApplicable = false
        )
        private const val EGG_VERTICAL = "Egg"
        private const val EGG_DROPSLOT_CONFIG_KEY = "egg-dropslot-config"
        val ONE_DAY_IN_MILLISECONDS = ChronoUnit.DAYS.duration.toMillis()
    }

    override suspend fun execute(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        logger.info("[Hook] Running DeliveryChargeFetchHook")
        if (context.getPlaceOrderPlans().isEmpty()) {
            return context.right()
        }
        val placeOrders = context.getPlaceOrderPlans().map { it.sellerOrder }
        val orderTotal = placeOrders.sumByLong { it.orderValueInPaisa() }
        val platformId = ModelV1.SellingPlatform.valueOf(context.getUserRequest().getReqPlatformId())
        val deliveryChargeResponse = kotlin.runCatching {
            logChargeProvider.getLogCharges(
                orders = placeOrders,
                shipToOrgUnitId = context.getToOrgUnitId(),
                orderTotalInPaise = orderTotal,
                sellingPlatform = platformId,
                applyDefaultLogCharge = false,
                deliveryChargeCartIdentifier = context.getDeliveryChargeCartIdentifier()
            ).await()
        }.getOrElse { exception ->
            logger.error("[DeliveryChargeFetchHook] Failed to fetch delivery charges: ${exception.message}", exception)
            DEFAULT_LOG_CHARGE
        }
        logger.info("[DeliveryChargeFetchHook] buyerOrgId:${context.getBuyerId()} orders = " +
            "${placeOrders.map {it.orderId}} shipToOrgUnitId = ${context.getToOrgUnitId()} deliveryChargeResponse: " +
            "$deliveryChargeResponse")
        if (deliveryChargeResponse.isApplicable.not()) {
            return nonEmptyListOf("Error: Unable to fetch delivery charges. Please try again").left()
        }
        storeOrderDeliveryCharges(context, deliveryChargeResponse, platformId)
        return context.right()
    }

    private suspend fun storeOrderDeliveryCharges(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge,
        platformId: SellingPlatform
    ) {
        if (deliveryChargeResponse.isApplicable) {
            when (deliveryChargeResponse.logChargeEntityType) {
                LogChargeEntityType.ORDER_ITEM_WEIGHT -> storeOrderDeliveryChargesForOrderItemType(
                    context, deliveryChargeResponse, platformId
                )

                LogChargeEntityType.ORDER_LADDER -> storeOrderDeliveryChargesForDropSlotType(
                    context, deliveryChargeResponse, platformId
                )
                LogChargeEntityType.DROPSLOT_DEFAULT -> storeOrderDeliveryChargesForDropSlotType(
                    context, deliveryChargeResponse, platformId
                )
                LogChargeEntityType.MAIN_ORDER -> storeOrderDeliveryChargesForMainOrderType(context, deliveryChargeResponse)
                LogChargeEntityType.DEFAULT -> storeOrderDeliveryChargesForDefaultType(context, deliveryChargeResponse)
                // TODO: Akhil
                else -> storeOrderDeliveryChargesForDefaultType(context, deliveryChargeResponse)
           }
        } else {
            logger.info("[storeOrderDeliveryCharges] Not valid log charges. Not stored in table")
        }
    }

    // For item based log charge, calculate log charge for each orderLine and store the sum in the order Level
    private fun storeOrderDeliveryChargesForOrderItemType(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge,
        platformId: SellingPlatform
    ) {
        val placeOrders = context.getPlaceOrderPlans().map { it.sellerOrder }
        val orderToOrderLineDeliveryChargesMap = placeOrders.associate { order ->
            val orderLineIds = order.orderLineList.map { orderLine -> orderLine.orderLineId }
            val itemLevelEntriesForOrder = deliveryChargeResponse.itemLevelLogCharge.filter {
                it.referenceId in orderLineIds
            }
            val orderLineDeliveryCharges = itemLevelEntriesForOrder.map { itemLevelDeliveryCharge ->
                val orderLine = order.orderLineList.first { it.orderLineId == itemLevelDeliveryCharge.referenceId }
                buildOrderLineDeliveryCharge(
                    order.orderId,
                    orderLine.orderLineId,
                    orderLine.units,
                    itemLevelDeliveryCharge,
                    platformId
                )
            }
            order.orderId to orderLineDeliveryCharges
        }
        val orderToOrderDeliveryChargesMap =
            orderToOrderLineDeliveryChargesMap
                .filter { (_, orderLineDeliveryChargesList) -> orderLineDeliveryChargesList.isEmpty().not() }
                .map {  (orderId, orderLineDeliveryChargeList) ->
                    val orderDeliveryCharge = orderLineDeliveryChargeList.sumByLong { it.deliveryChargeAmountInPaisa }
                    orderId to buildOrderDeliveryCharge(
                        orderId,
                        deliveryChargeResponse.logChargeEntityType,
                        orderDeliveryCharge,
                        deliveryChargeResponse.chargePayer,
                        platformId
                    )
                }.toMap().toMutableMap()
        // In case of order split where some split orders have no log charges, we need to store them as DROPSLOT_DEFAULT
        // orders with 0 log charge.
        placeOrders
                .map { it.orderId }
                .subtract(orderToOrderDeliveryChargesMap.keys)
                .toList()
                .map { orderId ->
                    orderToOrderDeliveryChargesMap.put(orderId, buildOrderDeliveryCharge(
                            orderId,
                            LogChargeEntityType.DROPSLOT_DEFAULT,
                            0L,
                            deliveryChargeResponse.chargePayer,
                            platformId
                    ))
                }
        context.setOrderDeliveryChargeDataMap(orderToOrderDeliveryChargesMap)
        context.setOrderLineDeliveryChargeDataMap(orderToOrderLineDeliveryChargesMap)
    }

    // For dropslot based log charge, determine if it is the first order in slot. If yes, charge 1 of the orders
    // and rest = 0. If no, all should be charged 0
    // there is no orderLine delivery charges
    private suspend fun storeOrderDeliveryChargesForDropSlotType(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge,
        platformId: SellingPlatform
    ) {
        val placeOrders = context.getPlaceOrderPlans().map { it.sellerOrder }
        val deliveryCharge = handleDeliveryChargeForDropSlot(context, deliveryChargeResponse)
        val maxOrderValue = placeOrders.maxOf { it.totalOrderSpPaise }
        val orderIdToCharge = placeOrders.first { it.totalOrderSpPaise == maxOrderValue }.orderId
        val orderToOrderDeliveryChargesMap = placeOrders.map { order ->
            val orderLogCharge = if (orderIdToCharge == order.orderId) deliveryCharge else 0L
            order.orderId to buildOrderDeliveryCharge(
                order.orderId,
                deliveryChargeResponse.logChargeEntityType,
                orderLogCharge,
                deliveryChargeResponse.chargePayer,
                platformId
            )
        }.toMap()
        context.setOrderDeliveryChargeDataMap(orderToOrderDeliveryChargesMap)
    }

    // For MainOrderType, don't store the details currently
    private fun storeOrderDeliveryChargesForMainOrderType(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge
    ) {
        return Unit
    }

    // For Default Type, don't store the details currently
    private fun storeOrderDeliveryChargesForDefaultType(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge
    ) {
        return Unit
    }

    private fun buildOrderDeliveryCharge(
        orderId: String,
        logChargeEntityType: LogChargeEntityType,
        deliveryChargeAmountInPaise: Long,
        chargePayer: LogChargePayer,
        platformId: SellingPlatform
    ): OrderDeliveryChargeData {
        return OrderDeliveryChargeData(
            entityId = orderId,
            entityType = DeliveryChargeEntityType.valueOf(logChargeEntityType.name),
            chargeStatus = DeliveryChargeStatus.CREATED,
            deliveryChargeAmountInPaise = deliveryChargeAmountInPaise,
            chargePayer = DeliveryChargePayer.valueOf(chargePayer.name),
            platformId = platformId,
            orderId = orderId
        )
    }

    private fun buildOrderLineDeliveryCharge(
        orderId: String,
        orderLineId: String,
        quantity: Int,
        itemLevelDeliveryCharge: ItemLevelDeliveryCharge,
        platformId: SellingPlatform
    ): OrderLineDeliveryCharge {
        val uqc = itemLevelDeliveryCharge.unitOfMeasurement?.let {
            when (it.name) {
                "KILOGRAM" -> UniqueQuantityCode.KILOGRAMS
                "GRAM" -> UniqueQuantityCode.GRAMS
                else -> UniqueQuantityCode.KILOGRAMS
            }
        } ?: UniqueQuantityCode.KILOGRAMS
        val perUnitDeliveryChargeAmountInPaisa = itemLevelDeliveryCharge.amountInPaisePerUnit?.let {
            it
        } ?: itemLevelDeliveryCharge.amountInPaise.div(quantity)
        return OrderLineDeliveryCharge(
            orderId = orderId,
            orderLineId = orderLineId,
            quantityOrdered = quantity.toDouble(),
            uqc = uqc,
            deliveryChargeAmountInPaisa = itemLevelDeliveryCharge.amountInPaise,
            perUnitDeliveryChargeAmountInPaisa = perUnitDeliveryChargeAmountInPaisa,
            platformId = platformId
        )
    }

    // Incase of Ladder or default dropslot, if there already exist an order in the dropslot,
    // don't charge the subsequent orders
    private suspend fun handleDeliveryChargeForDropSlot(
        context: CheckoutContext<CheckoutReqDto>,
        deliveryChargeResponse: LogCharge
    ): Long {
        return if (deliveryChargeResponse.logChargeEntityType in listOf(
                LogChargeEntityType.ORDER_LADDER,
                LogChargeEntityType.DROPSLOT_DEFAULT
            )
        ) {
            val buyerOrg = context.getBuyerOrg()

            if (hasActiveOrdersInDropSlot(
                    listingsMap = context.getListingsMap(),
                    buyerOrgUnit = buyerOrg?.orgUnitsMap?.get(context.getToOrgUnitId()),
                    buyerId = context.getBuyerId(),
                    orderIds = context.getPlaceOrderPlans().map { it.sellerOrder.orderId }
                )
            ) {
                0L
            } else {
                deliveryChargeResponse.deliveryChargesInPaise
            }
        } else {
            deliveryChargeResponse.deliveryChargesInPaise
        }
    }

    private suspend fun hasActiveOrdersInDropSlot(
        listingsMap: Map<String, ModelV2.TradeListing>,
        buyerOrgUnit: ModelV1.OrgUnit?,
        buyerId: String,
        orderIds: List<String>
    ): Boolean {
        val isHorecaBuyer = orgIdentityProvider.getOrgIdentityModel(buyerId)?.businessType == OrgBusinessType.HORECA
        val categories = if(isHorecaBuyer) {
            setOf(FOOD_AND_FMCG_CATEGORY, FRESH_CATEGORY, MEAT_CATEGORY)
        } else {
            listingsMap.values.parallelMap { listing ->
                categoryConfigHelper.getCategory(listing, null, null)
            }.mapNotNull { category ->
                if (category == FMCG_CATEGORY) FOOD_AND_FMCG_CATEGORY else category }
        }.toSet()

        val listingAndCategoryBySeller = listingsMap.values.map {
            it.orgId to Pair(
                it.listingId,
                verticalCache.getVerticalAsync(it.vertical).awaitOrNull()?.toSupplyChainCategory()
            )
        }
        val hasEggProducts = listingsMap.values.any {
            it.vertical == EGG_VERTICAL
        }
        val hasNonEggProducts = listingsMap.values.any {
            it.vertical != EGG_VERTICAL
        }
        if (hasEggProducts && hasNonEggProducts) {
            logger.warn("Current request has mix of egg and non egg products -> orderIDs = $orderIds")
        }
        val scCategories = when {
//            isHorecaBuyer -> setOf(SupplyChainCategory.FOOD, SupplyChainCategory.FRESH, SupplyChainCategory.MEAT)
            hasEggProducts.not() -> {
                listingAndCategoryBySeller
                    .mapNotNull { item ->
                        val listingAndCategoryPair = item.second
                        val category = listingAndCategoryPair.second
                        category
                    }
                    .toSet()
            }
            else -> {
                // As Eggs is part of multiple categories, it automatically gets selected into Food
                // but SupplyChain treats it as Fresh
                // Also Eggs are supposed to be in a different cart from other items
                setOf(SupplyChainCategory.FRESH)
            }
        }

        logger.info("SC Categories of cart: $scCategories")
        // Evaluate egg dropslot logic only if current order has eggs in cart and egg's dropslot experiment is enabled
        val checkEggsDropslot = hasEggProducts && eggsDropslotExperimentEnabled()
        val isAnyActiveOrdersInDropSlot = scCategories.parallelMap { scCategory ->
            val promiseCutOff = preOrderEtaProvider.fetchPreOrderEta(
                category = scCategory,
                buyerPincode = buyerOrgUnit?.unitAddress?.pincode,
                buyerOrgUnitId = buyerOrgUnit?.orgUnitId,
                platform = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
            )
            categories.any { category ->
                val ordersExistInDropSlot = dropslotServiceClient.getBuyerDropSlotsV2(
                    buyerId = buyerId,
                    orgUnitId = buyerOrgUnit?.orgUnitId ?: "",
                    pointSla = promiseCutOff,
                    categoryId = category
                ).executeAwait()
                // Hack -  Adding 1 day to the delivery eta to check if there are any orders in the dropslot
                // To handle case of buyerWeeklyOff until getPreOrderEtaForBuyer handles buyerWeeklyOff
                val nextDayCutOff = promiseCutOff + ONE_DAY_IN_MILLISECONDS
                val ordersExistInDropSlotNextDay = dropslotServiceClient.getBuyerDropSlotsV2(
                    buyerId = buyerId,
                    orgUnitId = buyerOrgUnit?.orgUnitId ?: "",
                    pointSla = nextDayCutOff,
                    categoryId = category
                ).executeAwait()
                val existingOrderIds = when {
                    ordersExistInDropSlot.orderIds
                        .isNullOrEmpty().not() -> ordersExistInDropSlot.orderIds
                    ordersExistInDropSlotNextDay.orderIds
                        .isNullOrEmpty().not() -> ordersExistInDropSlotNextDay.orderIds
                    else -> emptyList()
                } ?: emptyList()
                logger.info(
                    "Drop slot details => buyerId = $buyerId orgUnitId = ${buyerOrgUnit?.orgUnitId} " +
                        "promiseCutOff = $promiseCutOff nextdayCutoff = $nextDayCutOff  categoryId = $category " +
                        "SupplyChainCategory = $scCategory ordersExistInDropSlot = $existingOrderIds"
                )
                when {
                    isHorecaBuyer -> existingOrderIds.isNotEmpty()
                    // this will work as the logic is eggs cart will have only eggs as items & is non Horeca
                    checkEggsDropslot && existingOrderIds.isNotEmpty() -> {
                        handleDropSlotEvaluationForEggs(existingOrderIds)
                    }
                    else -> existingOrderIds.isNotEmpty()
                }
            }
        }.any { it == true }
        return isAnyActiveOrdersInDropSlot
    }

    // Hack - Initially egg is part of the same cart as Fresh or meat
    // But later egg is made as part of a different cart, and it is supposed to have flat log charge while fresh will
    // have item level charges.
    // This method will handle identification of existing orders in the dropslot for eggs as there is no
    // differentiation in supply chain between egg and fresh
    suspend fun handleDropSlotEvaluationForEggs(existingOrderIds: List<String>): Boolean {
        val orders = orderFormReadRepo.findByIds(
            orderIds = existingOrderIds,
            loadOrderLines = true,
            filterDraftOrders = false
        )
        val listingIdsOfExistingOrders = orders.map { order ->
            order.orderLineList.map { it.listingId }
        }.flatten().distinct()
        listingIdsOfExistingOrders.map { listingId ->
            val vertical = verticalCache.getVerticalForListing2(listingId)
            if (vertical?.name == EGG_VERTICAL) {
                return true
            }
        }
        return false
    }

    // This method returns if dropSlot for eggs is enabled or not
    suspend fun eggsDropslotExperimentEnabled(): Boolean {
        return kotlin.runCatching {
            val config = businessConfigClient.getStringAsync(EGG_DROPSLOT_CONFIG_KEY).await()?.let { config ->
                objectMapper.readValue(config, ConfigManager::class.java)
            }
            if (config?.isEnabled == true) {
                val currentTime = System.currentTimeMillis()
                val startCondition = config.startEpochInMillis?.let { startTime ->
                    startTime < currentTime
                } ?: true
                val endCondition = config.endEpochInMillis?.let { endTime ->
                    currentTime < endTime
                } ?: true
                startCondition && endCondition
            } else {
                false
            }
        }.getOrElse { exception ->
            logger.error("Failed to read egg dropslot config", exception)
            false
        }
    }

    data class ConfigManager(
        val isEnabled: Boolean,
        val startEpochInMillis: Long? = null,
        val endEpochInMillis: Long? = null
    )
}
