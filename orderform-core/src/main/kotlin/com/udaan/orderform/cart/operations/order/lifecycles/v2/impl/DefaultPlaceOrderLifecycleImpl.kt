package com.udaan.orderform.cart.operations.order.lifecycles.v2.impl

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.orderform.cart.context.ContextFactory
import com.udaan.orderform.cart.hooks.Hook
import com.udaan.orderform.cart.hooks.impl.NewOrderEventHook
import com.udaan.orderform.cart.hooks.impl.OrderStateValidationHook
import com.udaan.orderform.cart.hooks.impl.PlatformValidationHook
import com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.impl.PrePlaceOrderValidationsHook
import com.udaan.orderform.cart.models.dto.ConfirmRequestV2Dto
import com.udaan.orderform.cart.operations.order.contexts.PlaceOrderContext
import com.udaan.orderform.cart.operations.order.contexts.SQLTransactionContext
import com.udaan.orderform.cart.operations.order.lifecycles.v2.AbstractPlaceOrderLifecycleV2
import com.udaan.orderform.cart.operations.order.lifecycles.v2.TransactionLifecycle
import com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.PlaceHookCollection
import com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.impl.*
import com.udaan.orderform.service.business.*
import com.udaan.orderform.service.exceptions.*
import kotlinx.coroutines.Dispatchers
import org.jdbi.v3.core.JdbiException
import org.skife.jdbi.v2.exceptions.TransactionFailedException

open class DefaultPlaceOrderLifecycleImpl @Inject constructor(
    injector: Injector,
    private val contextFactory: ContextFactory,
    private val reservationManager: ReservationManager,
    private val placeOrderBizLogic: PlaceOrderBizLogic,
    @Named("default_tx_lifecycle")
    transactionLifecycle: TransactionLifecycle,
    private val placeLifecycleHandler: PlaceLifecycleHandler,
) : AbstractPlaceOrderLifecycleV2<ConfirmRequestV2Dto>(transactionLifecycle) {
    companion object {
        private val logger by logger()
    }

    @Suppress("UNCHECKED_CAST")
    private val preOrderFetchHooks = listOf(
        injector.getInstance(PreOrderFetchValidationsHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postConfirmHooks = listOf(
        injector.getInstance(AddATPLinesHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(ApplyPromotionsHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(RedeemRewardsHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postTransactionHooks = listOf(
        injector.getInstance(NewOrderEventHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>
    )

    private val rollbackHooks = listOf(
        injector.getInstance(CreditUnblockHook::class.java),
        injector.getInstance(RemoveATPLinesHook::class.java),
    )

    @Suppress("UNCHECKED_CAST")
    private val postReservationCheckHooks = listOf(
        injector.getInstance(PromiseInfoHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postFetchHooks = listOf(
        injector.getInstance(PlatformValidationHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(OrderStateValidationHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(PrePlaceOrderValidationsHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(FraudBuyerValidationHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
        injector.getInstance(OrderValueThresholdHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postOrderSplitHooks = listOf(
        injector.getInstance(MinimumOrderValueThresholdValidationHook::class.java),
        injector.getInstance(OrderGroupingHook::class.java) as Hook<PlaceOrderContext<ConfirmRequestV2Dto>>,
    )

    override suspend fun setupHooks(
        hooks: PlaceHookCollection<PlaceOrderContext<ConfirmRequestV2Dto>>,
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): PlaceHookCollection<PlaceOrderContext<ConfirmRequestV2Dto>> {
        setPostTransactionHooks(postTransactionHooks)
        setRollbackHooks(rollbackHooks)
        return hooks.copy(
            doPreOrderFetch = preOrderFetchHooks.plus(hooks.doPreOrderFetch),
            doPostOrderFetch = postFetchHooks.plus(hooks.doPostOrderFetch),
            doPostPlaceOrder = hooks.doPostPlaceOrder.plus(postConfirmHooks),
            doPostOrderSplit = hooks.doPostOrderSplit.plus(postOrderSplitHooks),
            doPostReservationChecks = hooks.doPostReservationChecks.plus(postReservationCheckHooks)
        )
    }

    override suspend fun doFetchOrder(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        try {
            val orders = context.getOrderContexts().values.parallelMap { it.getOrder() }.filterNotNull()
            if (orders.size != context.orderIds.size) {
                val missingOrders = context.request.orderIds.subtract(orders.map { it.orderId }.toSet())
                return nonEmptyListOf("No order found with id ${missingOrders.joinToString()}").left()
            }
            return context.right()
        } catch (e: Exception) {
            logger.error("[doFetchOrder] failed to fetch order details: ", e)
            return nonEmptyListOf("Unable to find order with id ${context.request.orderIds.joinToString()}").left()
        }
    }

    override suspend fun doValidations(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        return context.right()
    }

    override suspend fun doReservationChecks(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        return kotlin.runCatching {
            val reservationsInfoMap = placeLifecycleHandler.validateReservations(
                ordersContext = context.getOrderContexts(),
                toOrgUnitId = context.request.toOrgUnitId,
            )
            reservationsInfoMap.forEach {
                context.addReservationsInfo(it.key, it.value)
            }
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to check reservations: ${e.message}", e)
            return nonEmptyListOf(
                context.request.orderIds.joinToString() +
                        " - Failed to confirm items reservation"
            ).left()
        }
    }

    override suspend fun doSplitOrder(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        return kotlin.runCatching {
            val splitOrderResponse = placeLifecycleHandler.splitOrders(
                ordersContext = context.getOrderContexts(),
                reservationsInfoMap = context.getOrderContexts().map { (orderId, _) ->
                    orderId to context.getReservationsInfo(orderId)
                }.toMap()
            )
            logger.info("[doSplitOrder] splitOrderResponse: $splitOrderResponse")
            context.setSplitOrderResponse(splitOrderResponse)
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to split orders: ${e.message}", e)
            nonEmptyListOf("Failed to place order").left()
        }
    }

    override suspend fun doPreparePlacePlan(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        return try {
            val placeOrderPlans = placeLifecycleHandler.preparePlacePlans(
                splitOrderResponse = context.getSplitOrderResponse(),
                reservationsInfoMap = context.getReservationsInfoMap(),
                placeOrderReq = PlaceOrderReq(
                    toOrgUnitId = context.request.toOrgUnitId,
                    billToOrgUnitId = context.request.billToOrgUnitId,
                    selectedOnlinePaymentType = context.request.selectedOnlinePaymentType,
                    paymentModes = context.request.paymentModes,
                    selectedDeliverySlotId = context.request.selectedDeliverySlotId,
                    selectedItemLevelDeliverySlot = null, //Not required as this would be used in mario
                    orderSlotMap = context.request.orderSlotMap,
                    needGSTIN = context.request.needGSTIN,
                    instruction = context.request.instruction,
                    fromOrgUnitId = context.request.fromOrgUnitId,
                    promiseInfoMap = context.getPromiseInfoMap(),
                    rewardLines = context.getRewardLines(),
                )
            )
            context.setOrderPlans(placeOrderPlans)
            context.right()
        } catch (e: Exception) {
            logger.error("[doPreparePlacePlan] failed to prepare order plan: ", e)
            kotlin.runCatching {
                context.getOrderContexts().values.map { orderContext ->
                    reservationManager.revertStatusToReserve(
                        context.getReservationsInfo(orderContext.getOrderId())
                    )
                }
            }.getOrElse { err ->
                logger.error("[doPreparePlacePlan] failed to reverse reservation state to reserved: ", err)
            }
            return nonEmptyListOf(handleException(e).message ?: "Unable to prepare order for confirmation").left()
        }
    }

    override suspend fun doPlaceOrder(
        context: PlaceOrderContext<ConfirmRequestV2Dto>
    ): Either<NonEmptyList<String>, PlaceOrderContext<ConfirmRequestV2Dto>> {
        return try {
            val placedOrdersResponse = TelemetryScope.async(Dispatchers.IO) {
                context.getTransactionContext()?.let { transactionContext ->
                    placeOrderBizLogic.executePlacePlans(
                        conn = (transactionContext as SQLTransactionContext).handle,
                        placeOrderPlans = context.getOrderPlans(),
                        orderDeliveryChargeDataMap = context.getOrderDeliveryChargeDataMap(),
                        orderLineDeliveryChargeDataMap = context.getOrderLineDeliveryChargeDataMap()
                    )
                } ?: throw ReservationsFailedException("Unable to start order confirmation process")
            }.await()
            val placedOrders = placedOrdersResponse.placedOrders
            placedOrders.map { order ->
                val reservationsInfo = context.getReservationsInfo(order.orderId)
                val updatedReservationsInfo = reservationManager
                    .updateStatusAfterConfirm(reservationsInfo)
                context.addReservationsInfo(order.orderId, updatedReservationsInfo)
            }
            context.setPlacedOrders(placedOrders = placedOrders)
            placedOrdersResponse.orderDeliveryCharge?.let {
                context.setOrderDeliveryCharge(orderDeliveryCharge = it)
            }
            val updatedOrdersContextMap = placedOrders.associate { order ->
                order.orderId to contextFactory.createOrderContext(order.orderId, order)
            }
            context.setOriginalOrderContexts(context.getOrderContexts())
            context.setOrderContexts(updatedOrdersContextMap)
            context.right()
        } catch (e: Exception) {
            logger.error("[doPlaceOrder] failed to place order: ", e)
            kotlin.runCatching {
                context.getOrderContexts().values.map { orderContext ->
                    reservationManager.revertStatusToReserve(
                        context.getReservationsInfo(orderContext.getOrderId())
                    )
                }
            }.getOrElse { err ->
                logger.error("[doPlaceOrder] failed to reverse reservation state to reserved: ", err)
            }
            return nonEmptyListOf(handleException(e).message ?: "Failed to place the order with unknown error").left()
        }
    }

    private fun handleException(e: Exception): Exception {
        logger.error("Handling failure for $e", e)
        return when {
            e is JdbiException -> {
                DBOperationFailedException("Unable to complete operation due to storage failures")
            }

            e.cause is TransactionFailedException -> {
                val ex = e.cause as TransactionFailedException
                var finalEx = e
                when (ex.cause) {
                    is BaseOrderFormException -> {
                        finalEx = ex.cause as BaseOrderFormException
                    }
                }
                finalEx
            }

            else -> e
        }
    }
}
