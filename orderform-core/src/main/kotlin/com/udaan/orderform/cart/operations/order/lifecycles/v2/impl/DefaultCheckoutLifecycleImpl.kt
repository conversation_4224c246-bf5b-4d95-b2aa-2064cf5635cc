package com.udaan.orderform.cart.operations.order.lifecycles.v2.impl

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.catalog.models.ModelV2
import com.udaan.common.server.DistributedCallContext
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.models.SellerOrderInternals
import com.udaan.orderform.cart.common.providers.ReservationFlagsBuilder
import com.udaan.orderform.cart.context.ContextFactory
import com.udaan.orderform.cart.db.repo.OrderFormWriteRepo
import com.udaan.orderform.cart.domain.OrderFormFactory
import com.udaan.orderform.cart.domain.OrderLineFactory
import com.udaan.orderform.cart.hooks.Hook
import com.udaan.orderform.cart.hooks.impl.NewOrderEventHook
import com.udaan.orderform.cart.hooks.impl.pharma.PharmaCheckoutPostReserveHook
import com.udaan.orderform.cart.models.dto.CheckoutReqDto
import com.udaan.orderform.cart.models.dto.ItemProduct
import com.udaan.orderform.cart.models.dto.isCPODPaymentSelected
import com.udaan.orderform.cart.operations.order.contexts.CheckoutContext
import com.udaan.orderform.cart.operations.order.contexts.SQLTransactionContext
import com.udaan.orderform.cart.operations.order.lifecycles.v2.AbstractCheckoutLifecycleV2
import com.udaan.orderform.cart.operations.order.lifecycles.v2.TransactionLifecycle
import com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.CheckoutHookCollection
import com.udaan.orderform.cart.operations.order.lifecycles.v2.hooks.impl.*
import com.udaan.orderform.cart.services.v2.responses.ExceptionHandlerFactory
import com.udaan.orderform.cart.services.v2.responses.ExceptionHandlerType
import com.udaan.orderform.common.providers.CatalogProvider
import com.udaan.orderform.service.business.PlaceOrderBizLogic
import com.udaan.orderform.service.exceptions.BaseOrderFormException
import com.udaan.orderform.service.exceptions.DBOperationFailedException
import com.udaan.orderform.service.exceptions.InvalidCategoryGroupException
import com.udaan.orderform.service.exceptions.ReservationsFailedException
import com.udaan.proto.models.ModelV1.SellingPlatform
import kotlinx.coroutines.Dispatchers
import org.jdbi.v3.core.JdbiException
import org.skife.jdbi.v2.exceptions.TransactionFailedException

class DefaultCheckoutLifecycleImpl @Inject constructor(
    injector: Injector,
    private val contextFactory: ContextFactory,
    private val orderFormFactory: OrderFormFactory,
    private val orderLineFactory: OrderLineFactory,
    private val writeRepo: OrderFormWriteRepo,
    private val reservationLifecycleHandler: ReservationLifecycleHandler,
    private val placeLifecycleHandler: PlaceLifecycleHandler,
    private val placeOrderBizLogic: PlaceOrderBizLogic,
    private val catalogProvider: CatalogProvider,
    private val reservationManager: ReservationManager,
    @Named("default_tx_lifecycle")
    private val transactionLifecycle: TransactionLifecycle,
    private val categoryConfigHelper: CategoryConfigHelper,
    private val exceptionHandlerFactory: ExceptionHandlerFactory,
) : AbstractCheckoutLifecycleV2<CheckoutReqDto>(transactionLifecycle) {
    companion object {
        private val logger by logger()
    }

    @Suppress("UNCHECKED_CAST")
    private val preValidations = listOf(
        injector.getInstance(PreOrderFetchValidationsHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postTransactionHooks = listOf(
        injector.getInstance(NewOrderEventHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>
    )

    private val rollbackHooks = listOf(
        injector.getInstance(CreditUnblockHook::class.java),
        injector.getInstance(RemoveReservationHook::class.java),
        injector.getInstance(RemoveATPLinesHook::class.java),
    )

    @Suppress("UNCHECKED_CAST")
    private val postOrderCreateHooks = listOf(
        injector.getInstance(PrePlaceOrderValidationsHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(FraudBuyerValidationHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(OrderValueThresholdHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
    )

    @Suppress("UNCHECKED_CAST")
    private val postReservationHooks = listOf(
        injector.getInstance(OrderGroupingHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(PharmaCheckoutPostReserveHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(CreateFreebieItemDeliverySlotHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
    )

    private val preReservationHooks = listOf(
        injector.getInstance(CreatePromoLinesPreReservationHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>
    )

    private val postPlaceOrderPlanHooks = listOf(
        injector.getInstance(DeliveryChargeFetchHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(CreatePromoLinesAndOrderChargeForCashbackEntities::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(SetDropLevelInvoiceHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(OrderValueValidationHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>
    )

    @Suppress("UNCHECKED_CAST")
    private val postConfirmHooks = listOf(
        injector.getInstance(AddATPLinesHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(ApplyPromotionsHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(RedeemRewardsHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>,
        injector.getInstance(TrackOrderlineAndPricingAuditIdsHook::class.java) as Hook<CheckoutContext<CheckoutReqDto>>
    )

    override suspend fun setupHooks(
        hooks: CheckoutHookCollection<CheckoutContext<CheckoutReqDto>>,
        context: CheckoutContext<CheckoutReqDto>
    ): CheckoutHookCollection<CheckoutContext<CheckoutReqDto>> {
        setPostTransactionHooks(postTransactionHooks)
        setRollbackHooks(rollbackHooks)
        return hooks.copy(
            doPreValidations = hooks.doPreValidations.plus(preValidations),
            doPostOrdersCreation = hooks.doPostOrdersCreation.plus(postOrderCreateHooks),
            doPostReservation = hooks.doPostReservation.plus(postReservationHooks),
            doPostPlaceOrderPlan = hooks.doPostPlaceOrderPlan.plus(postPlaceOrderPlanHooks),
            doPostPlaceOrder = hooks.doPostPlaceOrder.plus(postConfirmHooks),
            doPreReservation = hooks.doPreReservation.plus(preReservationHooks)
        )
    }

    override suspend fun doFetchProducts(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        val listingSalesUnitIdPairs = context.request.products.map {
            it.listingId to it.salesUnitId
        }.groupBy { it.first }.map {
            it.key to it.value.map { it.second }
        }
        val listingsMap = catalogProvider.getMultipleListings(listingSalesUnitIdPairs).associateBy { it.listingId }
        context.setListingsMap(listingsMap)
        val listingIdToPricingAuditIdsMap = context.request.products.mapNotNull {
            val auditPricingId = it.auditPricingId
            if (auditPricingId != null) {
                Triple(
                    it.listingId,
                    it.salesUnitId,
                    auditPricingId
                )
            } else
                null
        }.associate {
            "${it.first}:${it.second}" to it.third
        }
        context.setPricingAuditIdsMap(listingIdToPricingAuditIdsMap)
        return context.right()
    }

    override suspend fun doCreateOrders(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        return kotlin.runCatching {
            val request = context.request
            val listingsMap = context.getListingsMap()

            val groupedBySellerOrg = context.request.products.groupBy { it.orgId }
            // this will not enable credit tenure split if all OLs have same credit tenure
            val enableOrderSplitByCreditTenure = context.request.products.distinctBy { it.creditTenure }.size > 1

            // assumption: all products will have same creditLineId
            // keeping creditLineId at product level allows to do split
            // at line id if a cart have multiple line items in future
            val creditLineId = context.request.products.firstOrNull()?.creditLineId

            val orders = groupedBySellerOrg.map { (sellerId, itemProductsGroupedAtSellerOrg) ->
                val isCPODPaymentSelected = context.request.additionalData.isCPODPaymentSelected()

                // Grouping by credit tenure is optional
                val orderToOrderLinesList = if (enableOrderSplitByCreditTenure) {
                    val groupedByCreditTenure = itemProductsGroupedAtSellerOrg.groupBy { it.creditTenure }
                    if (creditLineId.isNullOrEmpty().not() && groupedByCreditTenure.any { it.key.isNullOrEmpty() }) {
                        val message = "While splitting orders (split at seller org and credit tenure): " +
                                "found invalid creditTenure: ${groupedByCreditTenure.filter { it.key.isNullOrEmpty() }}" +
                                "for creditLineId: $creditLineId for Trade Credit (TC)"
                        logger.info(message)
                        // TODO: throw exception here
                    }
                    val orderAndOrderLinesPairs = groupedByCreditTenure
                            .map { (creditTenure, itemProducts) ->
                                buildOrderAndOrderLines(
                                        creditTenure = creditTenure,
                                        itemProducts = itemProducts,
                                        sellerId = sellerId,
                                        request = request,
                                        listingsMap = listingsMap,
                                        creditLineId = creditLineId,
                                        cpodPaymentSelected = isCPODPaymentSelected
                                )
                            }

                    val (orderIds, creditTenures) = orderAndOrderLinesPairs
                            .map { (order, _) -> order.orderId to order.extraData.creditTenurePeriod }
                            .unzip()

                    val splitOrderSize = orderIds.size

                    val message = "Created orders (split at seller org and credit tenure): " +
                            "$orderIds split order size: $splitOrderSize with creditTenure: $creditTenures" +
                            "and creditLineId: $creditLineId"
                    logger.info(message)
                    orderAndOrderLinesPairs // order (and associated OLs) split at seller org followed by credit tenure
                } else {
                    // If not using grouped by credit tenure, build orders without it

                    //this is needed to be passed if the items added are of same variable credit tenure
                    val creditTenure = itemProductsGroupedAtSellerOrg.firstOrNull()?.creditTenure

                    val orderAndOrderLinesPairs = listOf(
                            buildOrderAndOrderLines(
                                    creditTenure = creditTenure,
                                    itemProducts = itemProductsGroupedAtSellerOrg,
                                    sellerId = sellerId,
                                    request = request,
                                    listingsMap = listingsMap,
                                    creditLineId = creditLineId,
                                    cpodPaymentSelected = isCPODPaymentSelected
                            )
                    )
                    logger.info("Created orders (split at seller org) ${orderAndOrderLinesPairs
                            .map { (order, _) -> order.orderId}} with creditTenure: $creditTenure" +
                            "and creditLineId: $creditLineId")
                    orderAndOrderLinesPairs // order (and associated OLs) split at seller org only
                }
                orderToOrderLinesList.map { (order, orderLines) ->
                    order.toBuilder()
                            .setTotalOrderSpPaise(orderLines.sumOf { it.orderLineSpPaise })
                            .clearOrderLine().addAllOrderLine(orderLines)
                            .build()

                }
            }.flatten()

            logger.info("Created orders(${orders.size}): $orders")
            val orderContextsMap = orders.associate { order ->
                order.orderId to contextFactory.createOrderContext(order.orderId, order)
            }
            context.setCreatedOrder(orders)
            context.setOrderContexts(orderContextsMap)
            context.setItemLevelDeliverySlot(context.request.itemLevelDeliverySlot)
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to create order, ${e.message}", e)
            nonEmptyListOf(handleException(e).message ?: "Failed to create order, kindly try again").left()
        }
    }

    override suspend fun doStore(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        context.getCreatedOrder().parallelMap { order ->
            writeRepo.create(order)
        }
        val orderContextsMap = context.getCreatedOrder().map { order ->
            order.orderId to contextFactory.createOrderContext(
                orderId = order.orderId,
                order = null
            )
        }.associate { (orderId, orderContext) ->
            orderContext.setBuyerOrgUnitId(context.request.buyerOrgUnitId)
            orderContext.setListingsMap(context.getListingsMap())
            orderId to orderContext
        }
        context.setOrderContexts(orderContextsMap)
        return context.right()
    }

    override suspend fun doReserve(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        val reservedOrderDetails = reservationLifecycleHandler.reserve(
            context.getOrderContexts(),
            context.request.buyerOrgUnitId,
            ReservationFlagsBuilder().useOrchestratorV3Api(true).build(),
        )
        if (reservedOrderDetails.errorsList.isNotEmpty()) {
            val orderIds = context.getOrderContexts().keys
            deleteOrders(orderIds = orderIds.toList())
            return nonEmptyListOf(reservedOrderDetails.errorsList.joinToString(", ")).left()
        }
        // Create new order context to load new order details from db
        val orderContextsMap = reservedOrderDetails.reservedOrderMap.values.map { order ->
            order.orderId to contextFactory.createOrderContext(
                orderId = order.orderId,
                order = null
            )
        }.associate { (orderId, orderContext) ->
            orderContext.setBuyerOrgUnitId(context.request.buyerOrgUnitId)
            orderContext.setListingsMap(context.getListingsMap())
            orderId to orderContext
        }
        context.setOrderContexts(orderContextsMap)
        return context.right()
    }

    override suspend fun doPreparePlacePlan(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        return kotlin.runCatching {
            val request = context.request
            val (reservationInfoMap, placeOrderPlans) = placeLifecycleHandler.preparePlansToPlaceOrders(
                ordersContext = context.getOrderContexts(),
                placeOrderReq = PlaceOrderReq(
                    toOrgUnitId = request.buyerOrgUnitId,
                    billToOrgUnitId = request.billToOrgUnitId,
                    selectedOnlinePaymentType = request.selectedOnlinePaymentType,
                    paymentModes = request.paymentModes,
                    selectedDeliverySlotId = request.deliverySlotId,
                    selectedItemLevelDeliverySlot = context.getItemLevelDeliverySlot(),
                    orderSlotMap = emptyMap(),
                    needGSTIN = true,
                    promiseInfoMap = context.getPromiseInfo(),
                    rewardLines = context.getRewardLines(),
                    instruction = request.instruction,
                ),
                reservationFlags = ReservationFlagsBuilder().useOrchestratorV3Api(true).build(),
            )
            reservationInfoMap.map { (orderId, reservationInfo) ->
                context.addReservationsInfo(orderId, reservationInfo)
            }
            context.setPlaceOrderPlans(placeOrderPlans)
            context.right()
        }.getOrElse { exception ->
            val errorMessage = exceptionHandlerFactory.getHandler(ExceptionHandlerType.GENERIC).handle(exception)
            nonEmptyListOf(errorMessage).left()
        }
    }

    override suspend fun doPlaceOrder(
        context: CheckoutContext<CheckoutReqDto>
    ): Either<NonEmptyList<String>, CheckoutContext<CheckoutReqDto>> {
        return kotlin.runCatching {
            val placedOrdersResponse = TelemetryScope.async(Dispatchers.IO) {
                context.getTransactionContext()?.let { transactionContext ->
                    placeOrderBizLogic.executePlacePlans(
                        (transactionContext as SQLTransactionContext).handle,
                        context.getPlaceOrderPlans(),
                        orderDeliveryChargeDataMap = context.getOrderDeliveryChargeDataMap(),
                        orderLineDeliveryChargeDataMap = context.getOrderLineDeliveryChargeDataMap()
                    )
                } ?: throw ReservationsFailedException("Unable to start order confirmation process")
            }.await()
            val placedOrders = placedOrdersResponse.placedOrders
            placedOrders.map { order ->
                val reservationsInfo = context.getReservationsInfo(order.orderId)
                val updatedReservationsInfo = reservationManager
                    .updateStatusAfterConfirm(reservationsInfo)
                context.addReservationsInfo(order.orderId, updatedReservationsInfo)
            }
            context.setPlacedOrders(placedOrders = placedOrders)
            placedOrdersResponse.orderDeliveryCharge?.let {
                context.setOrderDeliveryCharge(orderDeliveryCharge = it)
            }
            val updatedOrdersContextMap = placedOrders.associate { order ->
                order.orderId to contextFactory.createOrderContext(order.orderId, order)
            }
            context.setOriginalOrderContexts(context.getOrderContexts())
            context.setOrderContexts(updatedOrdersContextMap)
            context.right()
        }.getOrElse { e ->
            logger.error("[doPlaceOrder] failed to place order: ", e)
            context.getOrderContexts().values.map { orderContext ->
                reservationManager.revertStatusToReserve(
                    context.getReservationsInfo(orderContext.getOrderId())
                )
            }
            val orderIds = context.getOrderContexts().keys
            logger.info("[doPlaceOrder] [checkoutLifecycle] deleting order: $orderIds")
            deleteOrders(orderIds = orderIds.toList())
            logger.info("[doPlaceOrder] [checkoutLifecycle] after deleting order: $orderIds")
            return nonEmptyListOf(handleException(e).message ?: "Failed to place the order with unknown error").left()
        }
    }

    private fun buildOrder(
            buyerId: String,
            sellerId: String,
            categoryGroupId: String,
            platformId: SellingPlatform,
            creditTenure: String?,
            creditLineId: String?,
            cpodPaymentSelected: Boolean
    ): ModelV1.SellerOrder {
        val createdBy = TelemetryScope.usingInheritedScopeOrFaked {
            coroutineContext[DistributedCallContext.Key]?.rootPrincipal
        }
        return orderFormFactory.buildOrderForm(
            buyerOrgId = buyerId,
            sellerOrgId = sellerId,
            categoryGroupId = categoryGroupId,
            orderParties = SellerOrderInternals.OrderParties.newBuilder().apply {
                this.agent = ""
                this.buyerId = buyerId
                this.createdBy = createdBy
            }.build(),
            platformId = platformId,
            creditTenure = creditTenure,
            creditLineId = creditLineId,
            cpodPaymentSelected = cpodPaymentSelected
        )
    }

    private suspend fun deleteOrders(orderIds: List<String>) {
        kotlin.runCatching {
            orderIds.map { orderId ->
                writeRepo.delete(orderId)
            }
        }.getOrElse { throwable ->
            logger.warn("Failed to delete draft orders: ${orderIds.map { it }} $throwable")
        }
    }

    private fun handleException(e: Throwable): Throwable {
        logger.error("Handling failure for $e", e)
        return when {
            e is JdbiException -> {
                DBOperationFailedException("Unable to complete operation due to storage failures")
            }

            e.cause is TransactionFailedException -> {
                val ex = e.cause as TransactionFailedException
                var finalEx = e
                when (ex.cause) {
                    is BaseOrderFormException -> {
                        finalEx = ex.cause as BaseOrderFormException
                    }
                }
                finalEx
            }

            else -> e
        }
    }

    private suspend fun buildOrderAndOrderLines(
            creditTenure: String?,
            itemProducts: List<ItemProduct>?,
            sellerId: String,
            request: CheckoutReqDto,
            listingsMap: Map<String, ModelV2.TradeListing>,
            creditLineId: String?,
            cpodPaymentSelected: Boolean
    ): Pair<ModelV1.SellerOrder, List<ModelV1.OrderLine>> {
        val categoryGroupId = itemProducts?.firstOrNull()?.let { itemProduct ->
            categoryConfigHelper.getCategoryGroup(itemProduct.listingId, null, null)
        } ?: throw InvalidCategoryGroupException("Server error: Insufficient category group info")

        val order = buildOrder(
                buyerId = request.buyerOrgId,
                sellerId = sellerId,
                categoryGroupId = categoryGroupId,
                platformId = request.platformId,
                creditTenure = creditTenure,
                creditLineId = creditLineId,
                cpodPaymentSelected = cpodPaymentSelected
        )

        val orderLines = itemProducts.mapNotNull { itemProduct ->
            listingsMap[itemProduct.listingId]?.let { listing ->
                orderLineFactory.buildOrderLine(
                        sellerOrderId = order.orderId,
                        salesUnitId = itemProduct.salesUnitId,
                        quantity = itemProduct.quantity,
                        userId = request.requester.userId,
                        promotionInfo = null, // TODO
                        listing = listing,
                        perUnitSpPaise = itemProduct.unitPriceInPaise,
                        puInfo = itemProduct.puInfo,
                        originalPerUnitPaise = itemProduct.prePromoUnitPriceInPaise,
                )
            }
        }

        return Pair(order, orderLines)
    }
}
