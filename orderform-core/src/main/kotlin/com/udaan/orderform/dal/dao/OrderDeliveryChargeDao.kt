package com.udaan.orderform.dal.dao

import com.udaan.common.jdbi3.KotlinMapperJdbi3
import com.udaan.orderform.models.*
import com.udaan.orderservice.models.OrderDeliveryCharge
import org.jdbi.v3.sqlobject.customizer.Bind
import org.jdbi.v3.sqlobject.customizer.BindBean
import org.jdbi.v3.sqlobject.statement.SqlQuery
import org.jdbi.v3.sqlobject.statement.SqlUpdate
import org.jdbi.v3.sqlobject.config.RegisterRowMapper
import java.util.*

class OrderDeliveryChargeMapper : KotlinMapperJdbi3<OrderDeliveryCharge>(OrderDeliveryCharge::class.java)

const val TABLE_ORDER_DELIVERY_CHARGES = "order_delivery_charges"

@RegisterRowMapper(OrderDeliveryChargeMapper::class)
interface OrderDeliveryChargeDao {
    @SqlUpdate(
        """
    INSERT INTO $TABLE_ORDER_DELIVERY_CHARGES
        (id, entity_id, entity_type, charge_status, delivery_charge_amount_in_paise, delivery_charge_collected, 
        current_active, payment_reference_id, charge_payer, order_id, created_at, updated_at) 
    VALUES 
        (:id, :entityId, :entityType, :chargeStatus, :deliveryChargeAmountInPaise, :deliveryChargeCollected, 
        :currentActive, :paymentReferenceId, :chargePayer, :orderId, :createdAt, :updatedAt)"""
    )
    fun create(
        @BindBean orderDeliveryCharge: OrderDeliveryCharge
    ): Int

    @SqlUpdate("UPDATE $TABLE_ORDER_DELIVERY_CHARGES " +
        "SET delivery_charge_amount_in_paise = :deliveryChargeAmountInPaise, updated_at = :updatedAt " +
        "WHERE id = :id and delivery_charge_collected = 0")
    fun update(
        @BindBean orderDeliveryCharge: OrderDeliveryCharge
    ): Int

    @SqlQuery("SELECT * FROM $TABLE_ORDER_DELIVERY_CHARGES WHERE id = :id")
    fun findById(@Bind("id") id: String): OrderDeliveryCharge
}