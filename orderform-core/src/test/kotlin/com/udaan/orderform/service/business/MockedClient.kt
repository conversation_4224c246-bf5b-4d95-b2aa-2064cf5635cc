package com.udaan.orderform.service.business

import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Injector
import com.microsoft.azure.eventhubs.EventHubClient
import com.udaan.cart.common.LogisticsProvider
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.RedisListingRepository
import com.udaan.catalog.client.combos.CatalogCombosClientV2
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.catalog.models.ModelV2
import com.udaan.catalog.representations.ApiV1
import com.udaan.catalog.representations.ApiV2
import com.udaan.catalog.representations.ListingTagsResponse
import com.udaan.common.server.getSync
import com.nhaarman.mockito_kotlin.any
import com.udaan.compliance.api.responses.TcsItTaxRateResponse
import com.udaan.compliance.client.ComplianceServiceClient
import com.udaan.config.client.BusinessConfigClient
import com.udaan.constraint.client.ConstraintClient
import com.udaan.constraint.models.dtos.MovResponseDto
import com.udaan.credit.client.CreditServiceClient
import com.udaan.credit.representations.CreditBuySummaryV2
import com.udaan.dropslot.client.DropslotServiceClient
import com.udaan.dropslot.representations.response.BuyerOrderDropSlotResponse
import com.udaan.featurestore.client.FeatureStoreClient
import com.udaan.featurestore.models.ServiceAPIResponse
import com.udaan.fulfilment.client.FulfilmentOrchestrationServiceClient
import com.udaan.fulfilment.client.ManagedFulfillmentServiceClient
import com.udaan.fulfilment.managed.CutoffDeliveryDataV2
import com.udaan.fulfilment.managed.FFModelType
import com.udaan.fulfilment.promise.Category
import com.udaan.inventory.api.v2.reservation.InventorySkuReservationState
import com.udaan.inventory.api.v2.reservation.PromisedInventoryTierType
import com.udaan.invoicing.client.InvoicingServiceClient
import com.udaan.invoicing.models.GoodsSupplyTypeResponse
import com.udaan.invoicing.models.SupplyType
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.listingtag.client.ListingTaggingClient
import com.udaan.logistics.client.LogisticsConsoleServiceClient
import com.udaan.logistics.client.LogisticsVSLClient
import com.udaan.logistics.common.utils.plusDays
import com.udaan.logistics.models.sla.mario.DeliveryEstimateBulkResponse
import com.udaan.logistics.models.sla.mario.DeliveryEstimateResponse
import com.udaan.logistics.models.vsl.ShippingChargeProfile
import com.udaan.logistics.models.vsl.ShippingProfileResponse
import com.udaan.logistics.promise.db.PincodeMasterDao
import com.udaan.logistics.representations.vsl.VSLRequest
import com.udaan.mariodistribution.client.MarioDistributionClient
import com.udaan.mariodistribution.client.helpers.SubscriptionWrapper
import com.udaan.mariodistribution.models.SubscriptionLifeCycle
import com.udaan.mariodistribution.representations.v1.CommitmentDetailsDTO
import com.udaan.mariodistribution.representations.v1.IncentiveDetails
import com.udaan.mariodistribution.representations.v1.MileStoneContext
import com.udaan.mariodistribution.representations.v1.SubscriptionV1DTO
import com.udaan.model.ExemptStatus
import com.udaan.model.IdentityStatus
import com.udaan.model.IdentityType
import com.udaan.om.dal.LaneSla
import com.udaan.om.dal.ProviderFinder
import com.udaan.orchestrator.client.AvailabilityServiceClient
import com.udaan.orchestrator.client.DeliverySlotsServiceClient
import com.udaan.orchestrator.client.PromiseServiceClient
import com.udaan.orchestrator.models.FFModelsInfo
import com.udaan.orchestrator.models.GetSlaPromise
import com.udaan.orchestrator.models.GetSlaResponse
import com.udaan.orchestrator.models.v2.DeliverySlotInfo
import com.udaan.orchestrator.models.v2.SupplyChainCategory
import com.udaan.order_mgt.client.LogisticsServiceClient
import com.udaan.order_mgt.events.model.PromiseInfoResponse
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.models.OrderLineLevy
import com.udaan.order_mgt.models.ShippingV1
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.order_mgt.models.states.StatesV1
import com.udaan.order_mgt.representations.OrderV1
import com.udaan.order_mgt.service.tiny_utils.Utils
import com.udaan.orderform.cart.common.providers.OrchestratorReservationProviderImpl
import com.udaan.orderform.cart.common.validators.AvailableCapacityValidator
import com.udaan.orderform.cart.context.ContextFactory
import com.udaan.orderform.cart.context.OrderExtractor
import com.udaan.orderform.cart.domain.business.mov.*
import com.udaan.orderform.cart.mocks.*
import com.udaan.orderform.cart.models.dto.ConfirmRequestV2Dto
import com.udaan.orderform.cart.models.dto.ReserveRequestV2Dto
import com.udaan.orderform.cart.operations.order.lifecycles.v2.AbstractPlaceOrderLifecycleV2
import com.udaan.orderform.cart.operations.order.lifecycles.v2.AbstractReserveLifecycleV2
import com.udaan.orderform.cart.operations.order.lifecycles.v2.impl.DefaultPlaceOrderLifecycleImpl
import com.udaan.orderform.cart.operations.order.lifecycles.v2.impl.DefaultReserveLifecycleImpl
import com.udaan.orderform.cart.operations.order.lifecycles.v2.validators.HazardousMaterialValidator
import com.udaan.orderform.cart.selectors.order.HorecaOrderFormSelector
import com.udaan.orderform.cart.selectors.order.OrderFormSelector
import com.udaan.orderform.cart.services.v2.responses.CartViolationData
import com.udaan.orderform.cart.services.v2.responses.PacmanResponseBuilder
import com.udaan.orderform.common.metrics.EventTracker
import com.udaan.orderform.common.models.ListingAvailability
import com.udaan.orderform.common.models.ListingData
import com.udaan.orderform.common.providers.*
import com.udaan.orderform.dal.repository.OrderFormDAL
import com.udaan.orderform.dal.repository.RouteDAL
import com.udaan.orderform.fulfillment.FulfilmentHelper
import com.udaan.orderform.fulfillment.ReservationHelper
import com.udaan.orderform.fulfillment.SplitOrderHelper
import com.udaan.orderform.models.allowOnlyProfileCheck
import com.udaan.orderform.models.strictFilter
import com.udaan.orderform.service.business.allocations.AllocatedInventoryManager
import com.udaan.orderform.service.business.allocations.AllocationValidator
import com.udaan.orderform.service.business.logistics.charges.DBRateCardWithConn
import com.udaan.orderform.service.business.logistics.charges.DynamicLogCharges
import com.udaan.orderform.service.business.logistics.charges.offers.ShippingChargeFreeOffers
import com.udaan.orderform.service.business.logistics.selection.ShippingProfileSelectorWithConn
import com.udaan.orderform.service.business.promotion.PromoContext
import com.udaan.orderform.service.business.promotion.PromoManager
import com.udaan.orderform.service.business.promotion.PromoManagerImpl
import com.udaan.orderform.service.business.risk.*
import com.udaan.orderform.service.business.risk.policies.MarioPrePaymentPolicy
import com.udaan.orderform.service.business.risk.policies.PrepaidOnlyListingPolicy
import com.udaan.orderform.service.cache.ListingCache
import com.udaan.orderform.service.ci
import com.udaan.orderform.service.events.OrderDeltaEventSender
import com.udaan.orderform.service.featureFlag.FeatureFlagManager
import com.udaan.orderform.service.helpers.ShippingProfileHelper
import com.udaan.orderform.service.logistic.LogisticInteractionFactory
import com.udaan.orderform.service.payments.OrderPaymentHandler
import com.udaan.orderform.service.utils.ChatHelper
import com.udaan.orderform.service.utils.IngestionUtils
import com.udaan.orderform.service.utils.MarioDistributionHelper
import com.udaan.payments.api.GroupPrepaymentCreationResponse
import com.udaan.payments.client.PaymentServiceClient
import com.udaan.pricing.PricingClient
import com.udaan.pricing_options.core.PricingOptionsClientV2
import com.udaan.pricing_options.model.PricingOptionsRequest
import com.udaan.pricing_options.model.PricingOptionsResponse
import com.udaan.promotions.api.models.*
import com.udaan.promotions.api.representations.*
import com.udaan.promotions.client.PromotionsServiceClient
import com.udaan.proto.tax.models.TaxV1
import com.udaan.representations.GSTResponse
import com.udaan.resources.RedisLettuce6Client
import com.udaan.rewards.client.RewardsClient
import com.udaan.rewards.models.CartModels
import com.udaan.storage.client.StorageServiceClient
import com.udaan.subscription.client.SubscriptionBuyerClient
import com.udaan.tax.core.biz.UdaanTaxCalculator
import com.udaan.tradequality.client.RiskProfileClient
import com.udaan.tradequality.client.OrderProfilingClient
import com.udaan.tradequality.client.TradeQualityBuyerRiskClient
import com.udaan.tradequality.models.riskProfiling.*
import com.udaan.user.client.DrugLicenseClient
import com.udaan.user.client.OrgServiceClient
import com.udaan.user.client.RedisOrgRepository
import io.mockk.*
import kotlinx.coroutines.future.await
import org.joda.time.DateTime
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService

data class MockedClient(
    val catalogServiceClient: CatalogServiceClient,
    val invoicingServiceClient: InvoicingServiceClient,
    val pricingOptionClient: PricingOptionsClientV2,
    val creditServiceClient: CreditServiceClient,
    val orgServiceClient: OrgServiceClient,
    val orgRepository: RedisOrgRepository,
    val logisticInteractionFactory: LogisticInteractionFactory,
    val paymentModeDecider: EligiblePaymentModeDeciderWithConn,
    val rateCard: DBRateCardWithConn,
    val orderEventHubClient: EventHubClient,
    val reIndexerEventHubClient: EventHubClient,
    val redisClientPool: RedisLettuce6Client,
    val orderDeltaEventSender: OrderDeltaEventSender,
    val storageServiceClient: StorageServiceClient,
    val orderPaymentHandler: OrderPaymentHandler,
    val shippingChargeFreeOffers: ShippingChargeFreeOffers,
    val orderFetcherLogic: OrderFetcherLogic,
    val pincodeMasterDao: PincodeMasterDao,
    val orderFormDal: OrderFormDAL,
    val rewardsClient: RewardsClient,
    val orchestrationServiceClient: FulfilmentOrchestrationServiceClient,
    val pendingOrdersManager: PendingOrdersManager,
    val pricingClient: PricingClient,
    val pricingService: PricingService,
    val catalogCombosClient: CatalogCombosClientV2,
    val allocatedInventoryManager: AllocatedInventoryManager,
    val objectMapper: ObjectMapper,
    val orderSplittingHelper: OrderSplittingHelper,
    val interCityPromiseHelper: InterCityPromiseHelper,
    val promoManager: PromoManager,
    val rewardsHelper: RewardsHelper,
    val fulfilmentHelper: FulfilmentHelper,
    val buyerConstraintsManager: BuyerConstraintsManager,
    val taxationBizLogicV2: TaxationBizLogicV2,
    val fulfilmentBizLogicV2: FulfillmentBizLogicV2,
    val riskDetector: PolicyBasedRiskDetector,
    val shippingProfileSelector: ShippingProfileSelectorWithConn,
    val routeDAL: RouteDAL,
    val chatHelper: ChatHelper,
    val deliverySlotsServiceClient: DeliverySlotsServiceClient,
    val orchestratorReservationProvider: OrchestratorReservationProviderImpl,
    val promiseServiceClient: PromiseServiceClient,
    val splitOrderHelper: SplitOrderHelper,
    val featureFlagManager: FeatureFlagManager,
    val promotionsServiceClient: PromotionsServiceClient,
    val verticalCache: VerticalCache,
    val categoryConfigHelper: CategoryConfigHelper,
    val riskProfileClient: RiskProfileClient,
    val complianceServiceClient: ComplianceServiceClient,
    val prepaidOnlyListingPolicy: PrepaidOnlyListingPolicy,
    val marioPrePaymentPolicy: MarioPrePaymentPolicy,
    val placeOrderBizLogic: PlaceOrderBizLogic,
    val shippingChargeProfile: ShippingChargeProfile,
    val orderFormBizLogic: OrderFormBizLogic,
    val subscriptionBuyerClient: SubscriptionBuyerClient,
    val udaanTaxCalculator: UdaanTaxCalculator,
    val listingTaggingClient: ListingTaggingClient,
    val constraintClient: ConstraintClient,
    val managedFulfillmentServiceClient: ManagedFulfillmentServiceClient,
    val dropslotServiceClient: DropslotServiceClient,
    val tradeQualityBuyerRiskClient: TradeQualityBuyerRiskClient,
    val marioDistributionClient: MarioDistributionClient,
    val marioDistributionHelper: MarioDistributionHelper,
    val allocationValidator: AllocationValidator,
    val logisticsProvider: LogisticsProvider,
    val inventoryProvider: InventoryProvider,
    val deliverySlotProvider: DeliverySlotProvider,
    val logChargeProvider: LogChargeProvider,
    val featureStoreClient: FeatureStoreClient,
    val ingestionUtils: IngestionUtils,
    val logisticsServiceClient: LogisticsServiceClient,
    val orderProfilingClient: OrderProfilingClient,
    val logisticsVSLClient: LogisticsVSLClient,
    val reservationHelper: ReservationHelper,
    val availabilityServiceClient: AvailabilityServiceClient,
    val horecaOrderFormSelector: OrderFormSelector,
    val horecaMovCalculator: Mov,
    val priceProvider: PriceProvider,
    val placeOrderPreValidations: PlaceOrderPreValidations,
    val dynamicLogCharges: DynamicLogCharges,
    val businessConfigClient: BusinessConfigClient,
    val availableCapacityValidator: AvailableCapacityValidator,
    val contextFactory: ContextFactory,
    val defaultReserveCycle: AbstractReserveLifecycleV2<ReserveRequestV2Dto>,
    val defaultOrderPlaceLifecycle: AbstractPlaceOrderLifecycleV2<ConfirmRequestV2Dto>,
    val hazardousMaterialValidator: HazardousMaterialValidator,
    val redisListingRepository: RedisListingRepository,
    val catalogProvider: CatalogProviderImpl,
    val paymentServiceClient: PaymentServiceClient,
    val pacmanMovCalculator: PacmanMovCalculator,
    val pacmanResponseBuilder: PacmanResponseBuilder,
    val eventTracker: EventTracker,
    val shippingProfileHelper: ShippingProfileHelper,
    val logisticsConsoleServiceClient: LogisticsConsoleServiceClient,
    val extractor: OrderExtractor,
    val drugLicenseClient: DrugLicenseClient
)


object MockHelper {
    private val randomString: UUID = UUID.randomUUID()

    fun createMocks(injector: Injector): MockedClient {
        fun <T> ci(kClass: Class<T>): T = injector.getInstance(kClass)
        val orgServiceClient = spyk(ci(OrgServiceClient::class.java))
        val catalogServiceClient = mockkClass(CatalogServiceClient::class)
        val availabilityServiceClient = mockkClass(AvailabilityServiceClient::class)
        val businessConfigClient = mockkClass(BusinessConfigClient::class)
        val logisticsVSLClient = mockkClass(LogisticsVSLClient::class)
        val fixedPoolExecService: ScheduledExecutorService = Executors.newScheduledThreadPool(16) { r ->
            Executors.defaultThreadFactory().newThread(r).also { it.isDaemon = true }
        }
        mockkStatic("com.udaan.orderform.models.ExtensionsKt")
        val shippingChargeProfile = mockkClass(ShippingChargeProfile::class)
        val orgRepository = mockkClass(RedisOrgRepository::class)
        val inventoryProvider = spyk(injector.ci(InventoryProviderMock::class.java))
        val deliverySlotProvider = spyk(injector.ci(DeliverySlotProviderMock::class.java))
        val logChargeProvider = spyk(injector.ci(LogChargeProviderMock::class.java))
        val catalogProvider = mockkClass(CatalogProviderImpl::class)
        val verticalCache = spyk(injector.ci(VerticalCache::class.java))
        val categoryConfigHelper = mockkClass(CategoryConfigHelper::class)
        val providerFinderList = slot<List<ProviderFinder>>()
        every {
            ShippingChargeProfile.ELECTRONICS.strictFilter(capture(providerFinderList))
        } answers {
            CompletableFuture.completedFuture(
                providerFinderList.captured
            ).getSync()
        }
        every {
            shippingChargeProfile.allowOnlyProfileCheck()
        } returns false
        val mockedEventhubClient = mockkClass(EventHubClient::class)
        return MockedClient(
            catalogServiceClient = catalogServiceClient,
            invoicingServiceClient = mockkClass(InvoicingServiceClient::class),
            pricingOptionClient = mockkClass(PricingOptionsClientV2::class),
            creditServiceClient = mockkClass(CreditServiceClient::class),
            orgServiceClient = orgServiceClient,
            orgRepository = orgRepository,
            logisticInteractionFactory = injector.ci(LogisticInteractionFactory::class.java),
            paymentModeDecider = mockkClass(EligiblePaymentModeDeciderWithConn::class),
            rateCard = injector.ci(DBRateCardWithConn::class.java),
            orderEventHubClient = mockedEventhubClient,
            reIndexerEventHubClient = mockedEventhubClient,
            redisClientPool = injector.ci(RedisLettuce6Client::class.java),
            orderDeltaEventSender = injector.ci(OrderDeltaEventSender::class.java),
            storageServiceClient = injector.ci(StorageServiceClient::class.java),
            orderPaymentHandler = injector.ci(OrderPaymentHandler::class.java),
            shippingChargeFreeOffers = injector.ci(ShippingChargeFreeOffers::class.java),
            orderFetcherLogic = injector.ci(OrderFetcherLogic::class.java),
            pincodeMasterDao = injector.ci(PincodeMasterDao::class.java),
            orderFormDal = injector.ci(OrderFormDAL::class.java),
            rewardsClient = mockkClass(RewardsClient::class),
            orchestrationServiceClient = mockkClass(FulfilmentOrchestrationServiceClient::class),
            promiseServiceClient = mockkClass(PromiseServiceClient::class),
            splitOrderHelper = mockkClass(SplitOrderHelper::class),
            featureFlagManager = mockkClass(FeatureFlagManager::class),
            pendingOrdersManager = mockkClass(PendingOrdersManager::class),
            pricingClient = mockkClass(PricingClient::class),
            pricingService = mockkClass(PricingService::class),
            catalogCombosClient = mockkClass(CatalogCombosClientV2::class),
            allocatedInventoryManager = injector.ci(AllocatedInventoryManager::class.java),
            objectMapper = injector.ci(ObjectMapper::class.java),
            orderSplittingHelper = spyk(injector.ci(OrderSplittingHelper::class.java)),
            interCityPromiseHelper = mockkClass(InterCityPromiseHelper::class),
            promoManager = spyk(injector.ci(PromoManagerImpl::class.java)),
            rewardsHelper = spyk(injector.ci(RewardsHelper::class.java)),
            buyerConstraintsManager = injector.ci(BuyerConstraintsManager::class.java),
            taxationBizLogicV2 = mockkClass(TaxationBizLogicV2::class),
            fulfilmentBizLogicV2 = spyk(injector.ci(FulfillmentBizLogicV2::class.java)),
//                riskDetector = PolicyBasedRiskDetector(orgServiceClient, ci(PrepaymentPoliciesStore::class.java), ci(LogisticsServiceClient::class.java)),
            riskDetector = mockkClass(PolicyBasedRiskDetector::class),
            shippingProfileSelector = spyk(
                ShippingProfileSelectorWithConn(ListingCache(catalogProvider, verticalCache))
            ),
            routeDAL = spyk(injector.ci(RouteDAL::class.java)),
            chatHelper = mockkClass(ChatHelper::class),
            deliverySlotsServiceClient = spyk(injector.ci(DeliverySlotsServiceClient::class.java)),
            orchestratorReservationProvider = spyk(injector.ci(OrchestratorReservationProviderImpl::class.java)),
            promotionsServiceClient = spyk(injector.ci(PromotionsServiceClient::class.java)),
            verticalCache = spyk(injector.ci(VerticalCache::class.java)),
            categoryConfigHelper = categoryConfigHelper,
            riskProfileClient = mockkClass(RiskProfileClient::class),
            prepaidOnlyListingPolicy = spyk(injector.ci(PrepaidOnlyListingPolicy::class.java)),
            marioPrePaymentPolicy = spyk(injector.ci(MarioPrePaymentPolicy::class.java)),
            placeOrderBizLogic = mockkClass(PlaceOrderBizLogic::class),
            shippingChargeProfile = shippingChargeProfile,
            orderFormBizLogic = mockkClass(OrderFormBizLogic::class),
            subscriptionBuyerClient = mockkClass(SubscriptionBuyerClient::class),
            udaanTaxCalculator = mockkClass(UdaanTaxCalculator::class),
            complianceServiceClient = mockkClass(ComplianceServiceClient::class),
            listingTaggingClient = mockkClass(ListingTaggingClient::class),
            constraintClient = mockkClass(ConstraintClient::class),
            managedFulfillmentServiceClient = mockkClass(ManagedFulfillmentServiceClient::class),
            dropslotServiceClient = mockkClass(DropslotServiceClient::class),
            tradeQualityBuyerRiskClient = mockkClass(TradeQualityBuyerRiskClient::class),
            marioDistributionClient = mockkClass(MarioDistributionClient::class),
            marioDistributionHelper = mockkClass(MarioDistributionHelper::class),
            allocationValidator = spyk(injector.ci(AllocationValidator::class.java)),
            fulfilmentHelper = spyk(
                FulfilmentHelper(
                    orgServiceClient,
                    ci(RedisLettuce6Client::class.java),
                    inventoryProvider = inventoryProvider,
                    orgRepository = orgRepository,
                    businessConfigClient = businessConfigClient,
                    logisticsProvider = LogisticsProviderMock()
                )
            ),
            logisticsProvider = LogisticsProviderMock(),
            inventoryProvider = inventoryProvider,
            deliverySlotProvider = deliverySlotProvider,
            logChargeProvider = logChargeProvider,
            featureStoreClient = mockkClass(FeatureStoreClient::class),
            ingestionUtils = mockkClass(IngestionUtils::class),
            logisticsServiceClient = mockkClass(LogisticsServiceClient::class),
            orderProfilingClient = mockkClass(OrderProfilingClient::class),
            logisticsVSLClient = logisticsVSLClient,
            reservationHelper = mockkClass(ReservationHelper::class),
            availabilityServiceClient = availabilityServiceClient,
            businessConfigClient = businessConfigClient,
            horecaOrderFormSelector = mockkClass(HorecaOrderFormSelector::class),
            horecaMovCalculator = mockkClass(HorecaMovImpl::class),
            priceProvider = mockkClass(PriceProvider::class),
            placeOrderPreValidations = mockkClass(PlaceOrderPreValidations::class),
            dynamicLogCharges = mockkClass(DynamicLogCharges::class),
            availableCapacityValidator = mockkClass(AvailableCapacityValidator::class),
            contextFactory = mockkClass(ContextFactory::class),
            defaultReserveCycle = mockkClass(DefaultReserveLifecycleImpl::class),
            defaultOrderPlaceLifecycle = mockkClass(DefaultPlaceOrderLifecycleImpl::class),
            hazardousMaterialValidator = mockkClass(HazardousMaterialValidator::class),
            redisListingRepository = mockkClass(RedisListingRepository::class),
            catalogProvider = catalogProvider,
            paymentServiceClient = mockkClass(PaymentServiceClient::class),
            pacmanMovCalculator = mockkClass(PacmanMovCalculator::class),
            pacmanResponseBuilder = mockkClass(PacmanResponseBuilder::class),
            eventTracker = spyk(injector.ci(EventTracker::class.java)),
            shippingProfileHelper = mockkClass(ShippingProfileHelper::class),
            logisticsConsoleServiceClient = mockkClass(LogisticsConsoleServiceClient::class),
            extractor = mockkClass(OrderExtractor::class),
            drugLicenseClient = mockkClass(DrugLicenseClient::class)
        )
    }

    fun prepareAllMockings(
        testData: OrderFormTestData,
        mockedClients: MockedClient
    ) {
        val vertical = com.udaan.catalog.models.ModelV1.Vertical.newBuilder().apply {
            this.metadataBuilder.apply {
                this.hasWeightBasedPricing = false
                this.hasMarginalGst = false
            }.build()
        }.build()
        val fulfilmentSellerOrder = slot<ModelV1.SellerOrder>()
        val now = Calendar.getInstance()
        val cutoffDeliveryDataV2 = CutoffDeliveryDataV2(
            cutOffTimeText = "5pm", deliveryDay = "Tomorrow",
            day = now.get(Calendar.DAY_OF_MONTH), month = now.get(Calendar.MONTH), year = now.get(Calendar.YEAR),
            cutoffElapsed = false, deliveryInUnix = now.timeInMillis
        )

        val rewardRespLineData = mockkClass(CartModels.LineDetail::class)

        val listingSlot = slot<String>()
        val includePrimarySlot = slot<Boolean>()
        val batchListingsSlot = slot<List<String>>()

        every {
            mockedClients.promiseServiceClient.getDeliveryDisplayMessage(any(), any())
        } returns ""

        coEvery { mockedClients.featureFlagManager.shouldShadowCallOrchestratorReservation(any()) } returns false
        coEvery { mockedClients.featureFlagManager.shouldRouteToOrchestratorReservation(any()) } returns false

        coEvery {
            mockedClients.promiseServiceClient.findSla(any()).executeAwait()
        } returns GetSlaResponse.SuccessResponse(
            listOf("").associateWith { GetSlaPromise(0) }
        )

        coEvery {
            mockedClients.logisticsConsoleServiceClient.getPromiseInfoDeliveryEstimateBulk(any()).executeAwait()
        } returns DeliveryEstimateBulkResponse(emptyList())

        coEvery {
            mockedClients.shippingProfileHelper.fetchShippingProfile(any())
        } returns ShippingChargeProfile.ELECTRONICS


        coEvery {
            mockedClients.redisListingRepository.getListing(capture(listingSlot))
        } answers {
            val listingId = listingSlot.captured
            listingSlot.clear()
            val listing = testData.listings.first { it.listingId == listingId }
            CompletableFuture.completedFuture(listing)
        }

        coEvery {
            mockedClients.redisListingRepository.getListing(capture(listingSlot))
        } answers {
            val listingId = listingSlot.captured
            listingSlot.clear()
            val listing = testData.listings.first { it.listingId == listingId }
            CompletableFuture.completedFuture(listing)
        }

        coEvery {
            mockedClients.catalogProvider.getListing(capture(listingSlot))
        } answers {
            val listingId = listingSlot.captured
            listingSlot.clear()
            val listing = testData.listings.first { it.listingId == listingId }
            listing
        }

        coEvery {
            mockedClients.catalogServiceClient.sendListingNotification(any(), any(), any()).executeAwait(any())
        } returns ApiV1.TradeListingCreateResponse.newBuilder().build()

        every {
            mockedClients.catalogServiceClient.sendListingNotification(any(), any(), any()).execute()
        } answers {
            CompletableFuture.completedFuture(ApiV1.TradeListingCreateResponse.newBuilder().build())
        }

        coEvery {
            mockedClients.catalogServiceClient.sendListingNotification(any(), any(), any()).executeAwait(any())
        } returns ApiV1.TradeListingCreateResponse.newBuilder().build()

        coEvery {
            mockedClients.catalogServiceClient.batchGetListing(capture(batchListingsSlot)).executeAwait(any())
        } answers {
            val listingId = batchListingsSlot.captured
            batchListingsSlot.clear()
            val filteredListings = testData.listings.filter { it.listingId in listingId }
            ApiV2.ListingBatchResponse.newBuilder().addAllListing(filteredListings).build()
        }

        coEvery {
            mockedClients.catalogServiceClient.batchGetListingV2(capture(batchListingsSlot)).executeAwait(any())
        } answers {
            val listingId = batchListingsSlot.captured
            batchListingsSlot.clear()
            val filteredListings = testData.listings.filter { it.listingId in listingId }
            ApiV2.ListingBatchResponse.newBuilder().addAllListing(filteredListings).build()
        }

        coEvery {
            mockedClients.catalogServiceClient.getListings(capture(listingSlot), any(), any(), any(), any())
                .executeAwait(any())
        } answers {
            val listingId = listingSlot.captured
            listingSlot.clear()
            val filteredListings = testData.listings.filter { it.listingId in listingId }
            ApiV2.ListingIteratorResponse.newBuilder().defaultInstanceForType
        }
        val availability = slot<ApiV2.AvailabilityBatchRequest>()
        every {
            mockedClients.catalogServiceClient.checkAvailability(capture(availability)).execute()
        } returns CompletableFuture.completedFuture(ApiV2.AvailabilityBatchResponse.getDefaultInstance())

        coEvery {
            mockedClients.invoicingServiceClient.getGoodsSupplyType(any(), any(), any(), any()).executeAwait()
        } answers {
            GoodsSupplyTypeResponse(SupplyType.INTRA_STATE, "")
        }

        every {
            mockedClients.availabilityServiceClient.fetchListingAvailabilityInfo(any(),).execute()
        } returns CompletableFuture.completedFuture(com.udaan.orchestrator.models.FFAvailabilityInfoResponse(emptyList()))

        coEvery {
            mockedClients.availabilityServiceClient.fetchListingAvailabilityInfo(any())
                .executeAwait()
        } returns com.udaan.orchestrator.models.FFAvailabilityInfoResponse(emptyList())

        coEvery {
            mockedClients.interCityPromiseHelper.findViolations(any(), any(), any(), any(), any(), any())
        } returns InterCityPromiseHelper.PromiseHelperResponse(
            emptyMap<ModelV1.OrderLine, InterCityPromiseViolation>(),
            null
        )

        coEvery {
            mockedClients.interCityPromiseHelper.checkViolations(any(), any(), any(), any(), any(), any())
        } returns Unit

        coEvery {
            mockedClients.interCityPromiseHelper.checkListingViolations(any(), any(), any(), any(), any(), any())
        } returns Unit

        coEvery {
            mockedClients.interCityPromiseHelper.getPromiseInfoForOrderLines(any(), any(), any(), any())
        } returns emptyList<PromiseInfoResponse>()

        coEvery {
            mockedClients.interCityPromiseHelper.findViolations(any(), any(), any(), any(), any(), any(), any())
        } coAnswers {
            emptyMap<String, InterCityPromiseViolation>() to null
        }

        every {
            mockedClients.verticalCache.getVerticalForListing(any())
        } returns vertical

//        coEvery {
//            mockedClients.verticalCache.getVerticalForListing2(any())
//        } returns vertical

        coEvery {
            mockedClients.verticalCache.getVerticalForListing2(any())
        } answers {
            com.udaan.catalog.models.ModelV1.Vertical.newBuilder().apply {
                this.addBaseVerticalBuilder().setBaseVerticalName("CN-Beverage")
                this.metadataBuilder.apply {
                    this.hasWeightBasedPricing = false
                    this.hasMarginalGst = false
                }.build()
            }.build()
        }


        val verticalSlot = slot<List<String>>()
        coEvery {
            mockedClients.verticalCache.getAllAsync(capture(verticalSlot))
        } answers {
            val verticalNames = verticalSlot.captured
            verticalSlot.clear()
            CompletableFuture.completedFuture(verticalNames.map { vn ->
                vn to com.udaan.catalog.models.ModelV1.Vertical.newBuilder().apply {
                    this.addBaseVerticalBuilder().setBaseVerticalName("CN-Beverage")
                    this.metadataBuilder.apply {
                        this.hasWeightBasedPricing = false
                        this.hasMarginalGst = false
                    }.build()
                }.build()
            }.toMap().toMutableMap())
        }

        val pricingRequest = slot<PricingOptionsRequest>()
        every {
            mockedClients.pricingOptionClient.getPricingOptions(capture(pricingRequest))
        } answers {
            val req = pricingRequest.captured
            pricingRequest.clear()
            val listing = req.tradeListing
            PricingOptionsResponse(listing, listOf(), listOf())
        }

        val pricingRequestSlot = slot<PricingOptionsRequest>()
        coEvery {
            mockedClients.pricingOptionClient.getPricingOptionsV2(capture(pricingRequestSlot))
        } answers {
            val req = pricingRequest.captured
            pricingRequest.clear()
            val listing = req.tradeListing
            PricingOptionsResponse(listing, listOf(), listOf())
        }

        coEvery {
            mockedClients.pricingOptionClient.getPricingOptionsV2(capture(pricingRequest))
        } answers {
            val req = pricingRequest.captured
            pricingRequest.clear()
            val listing = req.tradeListing
            PricingOptionsResponse(listing, listOf(), listOf())
        }

        val orgIdRequest = slot<String>()
        every {
            mockedClients.orgServiceClient.getOrgExtendedWithAllOrgUnits(capture(orgIdRequest)).execute()
        } answers {
            val req = orgIdRequest.captured
            orgIdRequest.clear()
            val buyerOrg = testData.buyers.firstOrNull { it.orgAccount.orgId == req }
            if (buyerOrg != null) {
                CompletableFuture.completedFuture(buyerOrg)
            } else {
                val sellerOrg = testData.sellers.firstOrNull { it.orgAccount.orgId == req }
                CompletableFuture.completedFuture(sellerOrg)
            }
        }
        coEvery {
            mockedClients.orgServiceClient.orgGSTExemptStatus(capture(orgIdRequest)).executeAwait()
        } answers {
            val req = orgIdRequest.captured
            orgIdRequest.clear()
            CompletableFuture.completedFuture(GSTResponse(ExemptStatus.EXEMPTED.name, ExemptStatus.EXEMPTED)).getSync()
        }

        every {
            mockedClients.orgRepository.getOrgExtended(capture(orgIdRequest))
        } answers {
            val req = orgIdRequest.captured
            orgIdRequest.clear()
            val buyerOrg = testData.buyers.firstOrNull { it.orgAccount.orgId == req }
            if (buyerOrg != null) {
                CompletableFuture.completedFuture(buyerOrg)
            } else {
                val sellerOrg = testData.sellers.firstOrNull { it.orgAccount.orgId == req }
                CompletableFuture.completedFuture(sellerOrg)
            }
        }

        every {
            mockedClients.orgRepository.getOrg(capture(orgIdRequest))
        } answers {
            val req = orgIdRequest.captured
            orgIdRequest.clear()
            val buyerOrg = testData.buyers.firstOrNull { it.orgAccount.orgId == req }
            if (buyerOrg != null) {
                CompletableFuture.completedFuture(buyerOrg.orgAccount)
            } else {
                val sellerOrg = testData.sellers.first { it.orgAccount.orgId == req }
                CompletableFuture.completedFuture(sellerOrg.orgAccount)
            }
        }

        val orgUnitIdRequest = slot<String>()
        every {
            mockedClients.orgRepository.getOrgUnit(capture(orgUnitIdRequest))
        } answers {
            val req = orgUnitIdRequest.captured
            orgUnitIdRequest.clear()
            val buyerOrg = testData.buyers.firstOrNull { it.orgAccount.orgUnitsMap.keys.contains(req) }
            if (buyerOrg != null) {
                CompletableFuture.completedFuture(buyerOrg.orgAccount.orgUnitsMap[req])
            } else {
                val sellerOrg = testData.sellers.first { it.orgAccount.orgUnitsMap.keys.contains(req) }
                CompletableFuture.completedFuture(sellerOrg.orgAccount.orgUnitsMap[req])
            }
        }

        coEvery {
            mockedClients.orgServiceClient.orgGSTExemptStatus(capture(orgIdRequest)).executeAwait()
        } answers {
            val req = orgIdRequest.captured
            orgIdRequest.clear()
            CompletableFuture.completedFuture(GSTResponse(ExemptStatus.EXEMPTED.name, ExemptStatus.EXEMPTED)).getSync()
        }

        every {
            mockedClients.orgServiceClient.prefClient(capture(orgIdRequest)).cartConditionPref().request.execute()
        } answers {
            orgIdRequest.clear()
            CompletableFuture.completedFuture(com.udaan.proto.models.ModelV1.CartConditions.getDefaultInstance())
        }

        every {
            mockedClients.orgServiceClient.prefClient(capture(orgIdRequest))
                .sellingConditionPrefRequest().request.execute()
        } answers {
            orgIdRequest.clear()
            CompletableFuture.completedFuture(com.udaan.proto.models.ModelV1.SellingConditions.getDefaultInstance())
        }

        every {
            mockedClients.orgServiceClient.prefClient(capture(orgIdRequest))
                .getPrefValue<com.google.protobuf.Message>(any(), any()).executeSync()
        } answers {
            CompletableFuture.completedFuture(com.udaan.proto.models.ModelV1.CartCheckout.getDefaultInstance())
                .getSync()
        }

        every {
            mockedClients.orgServiceClient.getAllIdentityData(any()).execute()
        } answers {
            val testOrgIdentity = com.udaan.model.OrgIdentity(
                orgId = "",
                orgUnitId = null,
                identityType = IdentityType.DLNO_20,
                value = null,
                userGSTExempted = false,
                status = IdentityStatus.DISABLED,
                data = null,
                createdAt = DateTime.now()
            )
            CompletableFuture.completedFuture(listOf(testOrgIdentity))
        }

        // logistics
        coEvery {
            mockedClients.routeDAL.getRoutes("", "")
        } returns ShippingV1.LogisticsRouteVslMaster.newBuilder().apply {
            this.zone = ShippingV1.ZONE.NR
        }.build()

        val shippingProfileRequest = slot<VSLRequest>()
        every {
            mockedClients.logisticsVSLClient.getShippingProfile(capture(shippingProfileRequest)).execute()
        } answers {
            CompletableFuture.completedFuture(
                ShippingProfileResponse(
                    shippingChargeProfile = ShippingChargeProfile.ELECTRONICS,
                    isDangerous = false,
                    orderVerticals = testData.listings.map { it.listingId })
            )
        }

        coEvery {
            mockedClients.routeDAL.getLaneInfoFor(ShippingV1.ZONE.NM)
        } returns LaneSla(ShippingV1.ZONE.NM.name, 2, 5, 4, 9)

        coEvery {
            mockedClients.dynamicLogCharges.logCharge(any(), any(), any(), any(), any(), any())
        } returns 100L

        val buyerOrgIdRequest = slot<String>()
//        coEvery {
//            mockedClients.logisticsProvider.getShippingProfile(any(), capture(orgIdRequest), capture(buyerOrgIdRequest))
//        } answers {
//            orgIdRequest.clear()
//            buyerOrgIdRequest.clear()
//            ShippingProfileResponse(ShippingChargeProfile.ELECTRONICS, false)
//        }
        val pCSlot = slot<PromoContext>()
        coEvery {
            mockedClients.promoManager.buildContext(listOf(), any())
        } returns PromoContext(listOf(), mapOf(), any())
        every {
            mockedClients.promoManager.capture(capture(pCSlot), any())
        } answers {
            val req = pCSlot.captured.getOrders()
            req
        }
        val sellerOrdersSlot = slot<List<ModelV1.SellerOrder>>()
        coEvery {
            mockedClients.paymentModeDecider.fetchMultiSellerPaymentModes(
                capture(sellerOrdersSlot),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } answers {
            val orderIdsList =
                listOf(sellerOrdersSlot.captured.first().orderId, sellerOrdersSlot.captured.last().orderId)
            sellerOrdersSlot.clear()
            CompletableFuture.completedFuture(
                listOf(
                    Triple(
                        PaymentGatewayV1.PaymentInstrument.COD,
                        orderIdsList,
                        mapOf(Pair("COD", listOf()))
                    )
                )
            )
        }
        // chat
        every {
            mockedClients.chatHelper.sendNotification(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns CompletableFuture.completedFuture(Unit)

        // credit
        every {
            mockedClients.creditServiceClient.getCreditLineSummary(any()).execute()
        } returns CompletableFuture.completedFuture(CreditBuySummaryV2())

        every {
            mockedClients.riskDetector.prepaymentRequired(any(), any(), any(), any(), any())
        } returns CompletableFuture.completedFuture(
            PrepaymentData(
                isPrepaymentRequired = false,
                prepaymentAmountPaise = 0L,
                isUdaanIntitate = false,
                prepaymentPercentage = 0,
                explainPrepaymentReason = null,
                appliedPolicy = "",
                enableAdvancePayment = false
            )
        )

        val sellerOrderSlot = slot<ModelV1.SellerOrder>()
        every {
            mockedClients.prepaidOnlyListingPolicy.isPrepaymentRequired(capture(sellerOrderSlot))
        } answers {
//            sellerOrderSlot.clear()
            CompletableFuture.completedFuture(false)
        }
        every {
            mockedClients.marioPrePaymentPolicy.isPrepaymentRequired(capture(sellerOrderSlot))
        } answers {
            sellerOrderSlot.clear()
            CompletableFuture.completedFuture(false)
        }

        every {
            mockedClients.promotionsServiceClient.applyPromotionsForOrder(any()).executeSync(3)
        } returns OrderPromotionsResponse("ODXX", emptyMap())
        val promoReqSlot = slot<OrderGroupPromoRequest>()
        every {
            mockedClients.promotionsServiceClient.applyPromotionsForOrderGroup(capture(promoReqSlot)).executeSync(3)
        } answers {
            OrderGroupPromotionsResponse(
                orderGroupId = promoReqSlot.captured.orderGroupId
            )
        }

        val promotionsServiceSlot = slot<OrderPromotionRequest>()
        every {
            mockedClients.promotionsServiceClient.getApplicableRewardDetailsV2(capture(promotionsServiceSlot)).execute()
        } answers {
            val req = promotionsServiceSlot.captured
            promotionsServiceSlot.clear()
            CompletableFuture.completedFuture(
                OrderPromotionsResponse(
                    orderId = req.sellerOrder.orderId,
                    orderLinePromotions = emptyMap(),
                    orderPromotions = emptyList(),
                    availableOrderLinePromos = emptyMap(),
                    availableOrderPromos = emptyList()
                )
            )
        }
        val promotionsServSlot = slot<OrderGroupPromoRequest>()
        coEvery {
            mockedClients.promotionsServiceClient.getApplicableRewardDetailsForOrderGroup(capture(promotionsServSlot))
                .executeAwait()
        } answers {
            val req = promotionsServSlot.captured
            promotionsServSlot.clear()
            OrderGroupPromotionsResponse(
                orderGroupId = req.orderGroupId
            )
        }

        val promoDetailsSlot = slot<String>()
        coEvery {
            mockedClients.promotionsServiceClient.lookupPromotion(capture(promoDetailsSlot)).executeAwait()
        } answers {
            val promoId = promoDetailsSlot.captured
            promoDetailsSlot.clear()
            TradePromotion(
                promotionId = promoId,
                title = "Promo Test",
                description = "Test promo description",
                termsAndConditions = "Promo Terms",
                summary = "Promo Summary",
                ownerOrgId = "",
                status = PromotionStatus.ACTIVE,
                startDate = ZonedDateTime.of(LocalDateTime.now(), ZoneId.of("Asia/Kolkata")),
                catalogTargeting = CatalogTargeting(),
                promoConditionType = PromotionConditionType.ORDER_LINE,
                promoTiers = listOf(
                    PromotionTier(
                        condition = OrderLinePromotionCondition(1),
                        reward = InstantFreebie(
                            ProductFreebie(
                                FreebieDetail(
                                    listingId = "",
                                    salesUnitId = "",
                                    billAmountInPaise = 0,
                                    monetaryValueInPaise = null,
                                    freebieQuantity = 1
                                )
                            )
                        )
                    )
                )
            )
        }

        every {
            mockedClients.promotionsServiceClient.cancelPromotionsForOrder(any()).executeSync()
        } returns CancelPromotionResponse("foo", false)

        every {
            mockedClients.fulfilmentBizLogicV2.splitOrderForWarehouseFulfilment(
                capture(fulfilmentSellerOrder),
                any()
            )
        } answers {
            val order = fulfilmentSellerOrder.captured
            fulfilmentSellerOrder.clear()
            val seller = testData.sellers.first { it.orgAccount.orgId == order.sellerId }
            CompletableFuture.completedFuture(
                order.orderLineList.map {
                    FcOrderLineSplit(
                        orderLine = it,
                        fcFulfilled = true,
                        reservationId = randomString.toString(),
                        fcId = randomString.toString(),
                        fcSkuId = randomString.toString(),
                        reservedSkuId = randomString.toString(),
                        sellerRegisteredUnitAtFc = seller.orgAccount.orgUnitsMap[seller.orgAccount.headOfficeOrgUnitRef]?.orgUnitId
                            ?: "",
                        errCodeOrderLineSplit = ErrCodeOrderLineSplit.NA,
                        tier = PromisedInventoryTierType.NOT_APPLICABLE,
                        fcType = StatesV1.FulfillmentCenter.FFC_UDWH
                    )
                }
            )
        }

        every {
            mockedClients.fulfilmentBizLogicV2.splitOrderForWarehouseFulfilmentV4(
                capture(fulfilmentSellerOrder),
                any(),
                any(),
                any()
            )
        } answers {
            val order = fulfilmentSellerOrder.captured
            fulfilmentSellerOrder.clear()
            val seller = testData.sellers.first { it.orgAccount.orgId == order.sellerId }
            CompletableFuture.completedFuture(
                order.orderLineList.map {
                    FcOrderLineSplit(
                        orderLine = it,
                        fcFulfilled = true,
                        reservationId = randomString.toString(),
                        fcId = randomString.toString(),
                        fcSkuId = randomString.toString(),
                        reservedSkuId = randomString.toString(),
                        sellerRegisteredUnitAtFc = seller.orgAccount.orgUnitsMap[seller.orgAccount.headOfficeOrgUnitRef]?.orgUnitId
                            ?: "",
                        errCodeOrderLineSplit = ErrCodeOrderLineSplit.NA,
                        tier = PromisedInventoryTierType.NOT_APPLICABLE,
                        fcType = StatesV1.FulfillmentCenter.FFC_UDWH
                    )
                }
            )
        }

        every {
            mockedClients.fulfilmentBizLogicV2.splitOrderForVariousFCFulfilment(
                any(),
                any(),
                any()
            )
        } returns CompletableFuture.completedFuture(listOf())
        every {
            mockedClients.fulfilmentBizLogicV2.splitOrderLinesForFulfillment(
                any(),
                any(),
                any()
            )
        } returns CompletableFuture.completedFuture(listOf())

        coEvery {
            mockedClients.availabilityServiceClient.fetchAvailableFFModels(any(), any()).executeAwait()
        } returns FFModelsInfo(listOf(FFModelType.ALL))

        every {
            mockedClients.rewardsClient.computeRewardPoints(any<CartModels.CartReq>()).execute()
        } returns CompletableFuture.completedFuture(CartModels.CartPoints(0, 0, 0, 0, listOf(rewardRespLineData)))
        val cartReq = slot<List<CartModels.CartReq>>()
        every {
            mockedClients.rewardsClient.redeemRewardPoints(capture(cartReq)).execute()
        } returns CompletableFuture.completedFuture(Unit)
        val sellerOrdersList = slot<List<ModelV1.SellerOrder>>()
        coEvery {
            mockedClients.rewardsHelper.redeemRewardsV2(capture(sellerOrdersList), any(), any(), any())
        } answers {
            CompletableFuture.completedFuture(Unit)
        }
        coEvery {
            mockedClients.rewardsHelper.redeemMultiSellerRewards(capture(sellerOrdersList), any(), any(), any())
        } answers {
            CompletableFuture.completedFuture(Unit)
        }
        val promoGroupSlot = slot<OrderGroupPromoRequest>()
        val promoContextSlot = slot<PromoContext>()
        coEvery {
            mockedClients.promoManager.applyPromotionsForGroup(capture(promoContextSlot), any())
        } answers {
            val req = promoGroupSlot.captured
            promoContextSlot.clear()
            promoGroupSlot.clear()
            CompletableFuture.completedFuture(
                OrderGroupPromotionsResponse(
                    orderGroupId = req.orderGroupId
                )
            )
        }
        coEvery {
            mockedClients.orderFormBizLogic.postReservationValidations(any(), any())
        } answers {
            CompletableFuture.completedFuture(Unit)
        }

        val ingestResponse = mockkClass(ServiceAPIResponse::class)
        coEvery {
            mockedClients.featureStoreClient.ingestToOnlineStore(any(), any()).executeAwait()
        } answers {
            ingestResponse
        }

        coEvery {
            mockedClients.logisticsServiceClient.hasUdaanExpressDeliveryServiceability(any()).executeAwait()
        } answers {
            true
        }

        every {
            mockedClients.orderFormBizLogic.validateAndSplit(any(), any(), true, any())
        } answers {
            CompletableFuture.completedFuture(
                Pair(
                    SplitOrderResponse(
                        any(),
                        listOf(FcOrder(any(), any(), false, false, any())),
                        any(),
                        any(),
                        false,
                        true,
                        any()
                    ), null
                )
            )
        }

        coEvery {
            mockedClients.pricingService.getContextualPrice(any(), any())
        } returns emptyList()

        coEvery {
            mockedClients.pricingService.getContextualPrice(any(), any(), any(), any())
        } returns emptyList()

        coEvery {
            mockedClients.pricingService.getContextualPriceV2(any(), any())
        } returns CompletableFuture.completedFuture(emptyList())

        coEvery {
            mockedClients.pricingService.getContextualPriceV2(any(), any(), any(), any())
        } returns CompletableFuture.completedFuture(emptyList())

        coEvery {
            mockedClients.pricingService.getContextualPriceWithRider(any(), any())
        } returns CompletableFuture.completedFuture(emptyList())

        coEvery {
            mockedClients.pricingService.buildPricingContext(any())
        } returns PricingContext(null, 0, null, null, null)

        every {
            mockedClients.pricingService.PRICING_CALL_EXEMPT_LISTING_STATUS
        } answers {
            setOf(ModelV2.TradeListing.Status.ACTIVE, ModelV2.TradeListing.Status.ACTIVE)
        }

        coEvery {
            mockedClients.pricingService.buildPricingContext(any(), any(), any())
        } returns PricingContext(null, 0, null, null, null)

        coEvery {
            mockedClients.pricingService.buildPricingContext(any(), any(), any(), any())
        } returns PricingContext(null, 0, null, null, null)

        coEvery {
            mockedClients.pricingService.enabled(any())
        } returns false

        val salesUnitSlot = slot<ModelV2.SalesUnit>()
        every {
            mockedClients.pricingService.unitPricePaise(capture(salesUnitSlot), any(), any())
        } answers {
            val salesUnits = salesUnitSlot.captured
            salesUnits.priceDetails.priceConditionList.first().unitPricePaise
        }

        every {
            mockedClients.pricingService.priceForSalesUnit(any(), any(), any())
        } returns null

        coEvery {
            mockedClients.pricingService.getContextualPriceV3(any(), any())
        } returns CompletableFuture.completedFuture(emptyList())

        every {
            mockedClients.pricingService.buildPricingContextWithQuantity(any(), any(), any())
        } returns PricingContext(null, 0, null, null, null)

        val categoryHelperListingSlot = slot<String>()
        val categoryHelperServerOrg = slot<com.udaan.proto.representations.OrgV1.OrgAccountExtendedResponse>()
        coEvery {
            mockedClients.categoryConfigHelper.getCategoryGroup(capture(categoryHelperListingSlot), any(), any())
        } returns CategoryGroupV2.ElectronicsAndAppliances.id

        coEvery {
            mockedClients.categoryConfigHelper.getCategoryGroup(
                capture(categoryHelperListingSlot),
                capture(categoryHelperServerOrg)
            )
        } returns CategoryGroupV2.ElectronicsAndAppliances.id

        val listingIdSlot = slot<String>()
        coEvery {
            mockedClients.categoryConfigHelper.getCategory(capture(listingIdSlot), any(), any())
        } returns CategoryGroupV2.ElectronicsAndAppliances.name

        coEvery {
            mockedClients.orderProfilingClient.shouldOrderBeSentForApproval(any()).executeAwait()
        } returns OrderOnApprovalResponse(false, null)

        coEvery {
            mockedClients.tradeQualityBuyerRiskClient.isOrderWithinThreshold(any()).executeAwait()
        } answers {
            OrderThresholdResponse(true, null)
        }
        coEvery {
            mockedClients.complianceServiceClient.getTcsItTaxRateFor(any()).executeAwait()
        } returns TcsItTaxRateResponse(
            tcsItRateCardId = "SOME_DUMMY_RATE_CARD_ID",
            tcsItTaxRate = BigDecimal.valueOf(0.01)
        )
        coEvery {
            mockedClients.taxationBizLogicV2.getTcsItTaxRateFor(any(), any(), any())
        } returns BigDecimal.valueOf(0.01)
        val listing = slot<ModelV2.TradeListing>()
        val orderLine = slot<ModelV1.OrderLine>()
        val orderTaxSlot = slot<OrderV1.GoodsTaxCalculateRequest>()

        every {
            mockedClients.udaanTaxCalculator.calculateGoodsTax(capture(orderTaxSlot))
        } answers {
            OrderV1.GoodsTaxCalculateResponse.newBuilder().apply {
                listingId = orderTaxSlot.captured.listingId
            }.build()
        }
        every {
            mockedClients.taxationBizLogicV2.getGoodsTaxDetails(capture(orderLine), capture(listing))
        } answers {

            OrderV1.GoodsTaxCalculateResponse.newBuilder().apply { listingId = listing.captured.listingId }
                .build().taxAmountDetailMap.entries.map { taxAmountDetail ->
                TaxV1.TaxLine.newBuilder().apply {
                    taxLineId = Utils.getUniqueIdWithPrefix("TXL")
                    taxableEntityId = orderLine.captured.orderLineId
                    sellerOrderId = orderLine.captured.sellerOrderId
                    mainOrderId = orderLine.captured.mainOrderId
                    taxAmountPaise = taxAmountDetail.value
                    taxType = TaxV1.TaxType.valueOf(taxAmountDetail.key)

                }.build()
            }
        }
        val supplyTypeSlot = slot<SupplyType>()
        val tcsItRateSlot = slot<BigDecimal>()
        every {
            mockedClients.taxationBizLogicV2.getAllLevies(
                capture(orderLine),
                capture(listing),
                capture(supplyTypeSlot),
                capture(tcsItRateSlot)
            )
        } answers {
            val taxBps = if (TaxationBizLogicV2.isGSTEnabled) {
                listing.captured.taxDetails.gstBps
            } else listing.captured.taxDetails.vatBps
            val lineItem = com.udaan.invoicing.math.LineItemPriceDetails.fromTaxableAmount(
                orderLine.captured.orderLineSpPaise, taxBps, supplyTypeSlot.captured,
                listing.captured.taxDetails.cessBps, 0, tcsItRateSlot.captured
            )
            lineItem.levies.map { (levyType, levy) ->
                OrderLineLevy(
                    id = Utils.getUniqueIdWithPrefix("OLL"),
                    orderId = orderLine.captured.sellerOrderId,
                    orderLineId = orderLine.captured.orderLineId,
                    levyType = levyType,
                    levyAmountPaise = levy.levyInPaise,
                    levyBps = levy.levyBps,
                    taxableAmountPaise = lineItem.priceExclusiveOfAllLeviesPaise,
                    listingId = orderLine.captured.listingId,
                    salesUnitId = orderLine.captured.salesUnitId,
                    amountInclusiveAllLeviesPaise = lineItem.priceInclusiveOfAllLeviesPaise
                )
            }
        }
        val listingsMap = slot<Map<String, ModelV2.TradeListing>>()
        coEvery {
            mockedClients.taxationBizLogicV2.getAllLeviesForOrder(
                capture(fulfilmentSellerOrder),
                capture(listingsMap),
                capture(supplyTypeSlot)
            )
        } coAnswers {
            val sellerOrder = fulfilmentSellerOrder.captured
            println("Captured sellerOrder: $sellerOrder")
            sellerOrder.orderLineList.flatMap { orderLine ->
                val listing = listingsMap.captured[orderLine.listingId]!!
                val taxBps = if (TaxationBizLogicV2.isGSTEnabled) {
                    listing.taxDetails.gstBps
                } else listing.taxDetails.vatBps
                val supplyType = supplyTypeSlot.captured
                val tcsItRate = BigDecimal(0.0)
                val lineItem = com.udaan.invoicing.math.LineItemPriceDetails.fromTaxableAmount(
                    orderLine.orderLineSpPaise,
                    taxBps,
                    supplyType, listing.taxDetails.cessBps, 0, tcsItRate
                )
                lineItem.levies.map { (levyType, levy) ->
                    OrderLineLevy(
                        id = Utils.getUniqueIdWithPrefix("OLL"),
                        orderId = orderLine.sellerOrderId,
                        orderLineId = orderLine.orderLineId,
                        levyType = levyType,
                        levyAmountPaise = levy.levyInPaise,
                        levyBps = levy.levyBps,
                        taxableAmountPaise = lineItem.priceExclusiveOfAllLeviesPaise,
                        listingId = orderLine.listingId,
                        salesUnitId = orderLine.salesUnitId,
                        amountInclusiveAllLeviesPaise = lineItem.priceInclusiveOfAllLeviesPaise
                    )
                }
            }
        }
        every {
            mockedClients.taxationBizLogicV2.getApplicableGoodsTax(any(), capture(listing))
        } answers {
            OrderV1.GoodsTaxCalculateResponse.newBuilder().apply {
                listingId = listing.captured.listingId
            }.build()
        }

        every {
            mockedClients.creditServiceClient.deleteCreditBlock(any(), any()).executeSync()
        } answers {
            CompletableFuture.completedFuture(false).getSync()
        }
        coEvery {
            mockedClients.subscriptionBuyerClient.isFreeShippingSubscriptionBuyer(any())
        } answers {
            true
        }

        coEvery {
            mockedClients.listingTaggingClient.getListingTags(any(), any()).executeAwait()
        } answers {
            ListingTagsResponse(tags = emptyList())
        }
        coEvery {
            mockedClients.constraintClient.calculateMov(any(), any(), any(), any(), any()).executeAwait(1)
        } returns MovResponseDto(amountInPaise = 0L)
        coEvery {
            mockedClients.managedFulfillmentServiceClient.isListingUdaanFulfilled(any()).executeAwait()
        } returns false
        coEvery {
            mockedClients.dropslotServiceClient.getBuyerDropSlotsV2(any(), any(), any(), any()).executeAwait()
        } returns BuyerOrderDropSlotResponse("", "", emptyList<String>())

        val orderValue = slot<Long>()
        coEvery {
            mockedClients.tradeQualityBuyerRiskClient.isBuyerWithinCODLimit(any(), capture(orderValue)).executeAwait()
        } answers {
            val orderVal = orderValue.captured
            orderValue.clear()
            orderVal > 3000000
        }

        val buyerOrgId = slot<String>()
        val listingIds = slot<List<String>>()
        coEvery {
            mockedClients.marioDistributionHelper.verifyEligibility(capture(buyerOrgId), capture(listingIds))
        } answers {
            val buyerId = buyerOrgId.captured
            val listings = listingIds.captured
            buyerOrgId.clear()
            listingIds.clear()
            listings.map { it to true }.toMap()
        }

        coEvery {
            mockedClients.marioDistributionHelper.verifyEligibilityForBuyer(capture(buyerOrgId))
        } answers {
            val buyerId = buyerOrgId.captured
            buyerOrgId.clear()
            true
        }

        coEvery {
            mockedClients.marioDistributionHelper.getSubscriptionDetails(any(), any())
        } answers {
            SubscriptionWrapper(
                SubscriptionV1DTO(
                    id = "SID1234567",
                    brandId = "",
                    sellerId = "",
                    buyerOrgId = "",
                    commitmentDetails = CommitmentDetailsDTO(10000, 10000),
                    startedAt = Instant.now(),
                    disabledAt = null,
                    createdAt = Instant.now(),
                    offerings = emptyList(),
                    tenure = 30,
                    completedAt = Instant.now().plusDays(30),
                    isSORActive = true,
                    status = SubscriptionLifeCycle.ACTIVE,
                    incentiveDetails = IncentiveDetails(
                        incentives = emptyList(),
                        mileStoneContext = MileStoneContext(
                            currentCompletionBps = 0,
                            distanceToNextTargetBps = 0,
                            distanceToNextTargetPaise = 0,
                            nextMileStoneType = null,
                            nextMileStoneBps = 0,
                            nextDiscountType = null
                        )
                    )
                )
            )
        }


        coEvery {
            mockedClients.deliverySlotsServiceClient.getDeliverySlotsByOrgUnitIdV2(any(), any(), any(), any())
                .executeAwait()
        } answers {
            emptyList<DeliverySlotInfo>()
        }

        val allocationsListingData = slot<List<ListingData>>()
        coEvery {
            mockedClients.inventoryProvider.checkAvailabilityAsync(
                any(),
                any(),
                any(),
                any(),
                capture(allocationsListingData)
            ).await()
        } coAnswers {
            val allocationsListingDataCaptured = allocationsListingData.captured
            allocationsListingDataCaptured.map { tempListing ->
                ListingAvailability(
                    listingId = tempListing.listingId,
                    salesUnitId = tempListing.salesUnitId,
                    isAvailable = true,
                    availableQuantity = 0L
                )
            }.associateBy { it.salesUnitId }
        }

        coEvery {
            mockedClients.deliverySlotsServiceClient.getDeliverySlotById(any()).executeAwait()
        } answers {
            DeliverySlotInfo(
                category = Category.FMCG,
                startMinuteOfDay = 0,
                endMinuteOfDay = 1260,
                excludedPincodes = emptyList(),
                metadata = emptyMap(),
                pincodePrefix = 56,
                currentActive = true,
                deliveryDay = 1,
                slotAvailable = true,
                slotId = "123"
            )
        }

        coEvery {
            mockedClients.orderProfilingClient.isOrderPlacementBlocked(any()).executeAwait()
        } answers {
            OrderBlockCheckResponse(blocked = false, messageToUser = null)
        }

        coEvery {
            mockedClients.orderProfilingClient.isOrderRisky(any()).executeAwaitOrNull()
        } answers {
            BuyerOrderRiskStatus(orderRisky = false, riskScore = 0.0)
        }

        coEvery {
            mockedClients.reservationHelper.getReservationStatus(any())
        } answers {
            InventorySkuReservationState.RESERVED
        }

        coEvery {
            mockedClients.placeOrderPreValidations.performPreValidations(any(), any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validatePharmaWhitelistedBuyer(any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.performPreValidations(any(), any(), any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validateHazardousMaterial(any(), any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validateAllListingsActive(any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validateAllListings(any(), any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validateMaxUnits(any(), any())
        } returns Unit

        coEvery {
            mockedClients.placeOrderPreValidations.validateListingBuyability(any(), any())
        } returns Unit

        coEvery { mockedClients.businessConfigClient.sIsMember(any(), any()) } returns false

        coEvery { mockedClients.businessConfigClient.sIsMemberAsync(any(), any()) } returns
                CompletableFuture.completedFuture(false)

        coEvery { mockedClients.businessConfigClient.getInt(any(), any()) } returns 70
        coEvery { mockedClients.drugLicenseClient.hasValidDrugLicenseInCache(any()).await() } answers {true}

        coEvery {
            mockedClients.availableCapacityValidator.validate(any())
        } answers {
            true.right()
        }

        coEvery {
            mockedClients.hazardousMaterialValidator.validate(any())
        } answers {
            false.right()
        }

        coEvery {
            mockedClients.paymentServiceClient.createGroupPrepaymentEntries(any()).executeAwait()
        } answers {
            GroupPrepaymentCreationResponse(
                groupId = "TEST1",
                sourceRefIds = emptyList(),
                totalAmountInPaise = 0L
            )
        }

        coEvery {
            mockedClients.pacmanMovCalculator.calculate(any(), any(), any(), any(), any(), any(), any())
        } answers {
            PacmanMov(
                strategy = PacmanMovStrategy.DEFAULT,
                amountInPaise = 0L,
            )
        }

        coEvery {
            mockedClients.pacmanResponseBuilder.buildMovViolations(any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any())
        } answers {
            CartViolationData(
                violations = emptyList(),
                movAmountInPaise = 0,
                hasActiveDeliverySlot = false,
                looseMovAmountInPaise = 0,
                bulkMovAmountInPaise = 0,
            )
        }

        coEvery {
            mockedClients.eventTracker.incrementCounter(any(), any())
        } returns Unit
    }
}
