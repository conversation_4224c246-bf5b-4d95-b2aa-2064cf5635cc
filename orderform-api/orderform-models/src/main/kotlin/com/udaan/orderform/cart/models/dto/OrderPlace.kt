package com.udaan.orderform.cart.models.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.order_mgt.models.ModelV1
import com.udaan.order_mgt.models.PromoSource
import com.udaan.order_mgt.models.SellerOrderInternals
import com.udaan.order_mgt.models.pg.PaymentGatewayV1

@JsonIgnoreProperties(ignoreUnknown = true)
data class Requester(
    val userId: String,
    val orgId: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReserveRequestV2Dto(
    val platformId: com.udaan.proto.models.ModelV1.SellingPlatform,
    val categoryGroupId: String,
    val requester: Requester,
    val orderIds: List<String>,
    val toOrgUnitId: String,
    val couponIds: List<String> = emptyList(),
) : BaseRequestDto {
    override fun getReqCategoryGroupId(): String {
        return categoryGroupId
    }

    override fun getReqPlatformId(): String {
        return platformId.name
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReserveResponseV2Dto(
    val ordersMap: Map<String, ModelV1.SellerOrder>,
    val failedOrders: List<String>? = null,
    val errorsList: List<String>? = null,
) : BaseResponseDto

interface BaseCheckoutReq : BaseRequestDto {
    fun useRewards(): Boolean

    fun getSelectedPaymentModes(): List<PaymentGatewayV1.PaymentInstrument>

    fun getBuyerId(): String

    fun getDeliveryOrgUnitId(): String

    fun getReqCouponIds(): List<String>

    fun getReqAdditionalData(): AdditionalData
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConfirmRequestV2Dto(
    val platformId: com.udaan.proto.models.ModelV1.SellingPlatform,
    val categoryGroupId: String,
    val requester: Requester,
    val orderIds: List<String>,
    val toOrgUnitId: String,
    val billToOrgUnitId: String? = null,
    val fromOrgUnitId: String? = null,
    val prepaymentRequired: Boolean,
    val prepaymentAmountInPaise: Long,
    val selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
    val paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
    val useRewardsWorth: Long,
    val selectedDeliverySlotId: String?,
    val couponIds: List<String> = emptyList(),
    val buyerIpAddress: String?,
    val instruction: String? = null,
    val needGSTIN: Boolean = true,
    val additionalData: AdditionalData = emptyMap(),
    val orderSlotMap: Map<String, String> = emptyMap(),
) : BaseCheckoutReq {
    override fun useRewards(): Boolean = useRewardsWorth > 0

    override fun getSelectedPaymentModes(): List<PaymentGatewayV1.PaymentInstrument> = paymentModes

    override fun getBuyerId(): String = requester.orgId

    override fun getDeliveryOrgUnitId(): String = toOrgUnitId

    override fun getReqAdditionalData(): AdditionalData = additionalData

    override fun getReqCategoryGroupId(): String {
        return categoryGroupId
    }

    override fun getReqCouponIds(): List<String> {
        return couponIds
    }

    override fun getReqPlatformId(): String {
        return platformId.name
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConfirmResponseV2Dto(
    val originalOrderIds: List<String>,
    val placedOrderIds: List<String>,
    val orderDeliveryCharge: OrderDeliveryChargeResponseDto? = null,
) : BaseResponseDto

data class OrderDeliveryChargeResponseDto(
    val orderDeliveryChargeId: String,
    val deliveryChargeInPaise: Long
)

data class RewardItem(
    val listingId: String,
    val salesUnitId: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemPromotion(
    val promotionId: String,
    val discountBps: Long,
    val rewardClass: String,
    val promoSource: PromoSource,
    val rewardedItems: List<RewardItem>, // Point to freebie items
    val isItemLevel: Boolean,
    val parentRewardItem: RewardItem? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemCashback(
    val identifier: String,
    val discountBps: Long,
    val rewardClass: String,
    val source: CashbackSource,
    val isItemLevel: Boolean
)

enum class CashbackSource {
    PROMOTIONS,
    UNKNOWN
}


@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemProduct @JvmOverloads constructor(
    val orgId: String,
    val listingId: String,
    val salesUnitId: String,
    val quantity: Int,
    val unitPriceInPaise: Long,
    val prePromoUnitPriceInPaise: Long,
    val puInfo: PUReqDto? = null,
    val promotions: List<ItemPromotion>,
    val creditTenure: String? = null,
    val creditLineId: String? = null,
    val auditPricingId: String? = null,
    val cashbacks: List<ItemCashback> = emptyList()
) {

    fun copy(
        orgId: String,
        listingId: String,
        salesUnitId: String,
        quantity: Int,
        unitPriceInPaise: Long,
        prePromoUnitPriceInPaise: Long,
        puInfo: PUReqDto?,
        promotions: List<ItemPromotion>,
        creditTenure: String?,
        creditLineId: String?
    ) = ItemProduct(
        orgId = orgId,
        listingId = listingId,
        salesUnitId = salesUnitId,
        quantity = quantity,
        unitPriceInPaise = unitPriceInPaise,
        prePromoUnitPriceInPaise = prePromoUnitPriceInPaise,
        puInfo = puInfo,
        promotions = promotions,
        creditTenure = creditTenure,
        creditLineId = creditLineId,
        auditPricingId = null
    )

    fun copy(
        orgId: String,
        listingId: String,
        salesUnitId: String,
        quantity: Int,
        unitPriceInPaise: Long,
        prePromoUnitPriceInPaise: Long,
        puInfo: PUReqDto?,
        promotions: List<ItemPromotion>,
        creditTenure: String?,
        creditLineId: String?,
        auditPricingId: String?
    ) = ItemProduct(
        orgId = orgId,
        listingId = listingId,
        salesUnitId = salesUnitId,
        quantity = quantity,
        unitPriceInPaise = unitPriceInPaise,
        prePromoUnitPriceInPaise = prePromoUnitPriceInPaise,
        puInfo = puInfo,
        promotions = promotions,
        creditTenure = creditTenure,
        creditLineId = creditLineId,
        auditPricingId = auditPricingId,
        cashbacks = emptyList()
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CheckoutReqDto(
    val platformId: com.udaan.proto.models.ModelV1.SellingPlatform,
    val buyerOrgId: String,
    val requester: Requester,
    val products: List<ItemProduct>,
    val buyerOrgUnitId: String,
    val billToOrgUnitId: String? = null,
    val useRewardsWorth: Long? = null,
    val selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
    val paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
    val deliverySlotId: String? = null,
    val couponIds: List<String> = emptyList(),
    val additionalData: AdditionalData = emptyMap(),
    val instruction: String? = null,
    val itemLevelDeliverySlot: Map<String, String>? = null,
    val deliveryChargeCartIdentifier: String? = null
) : BaseCheckoutReq {
    override fun useRewards(): Boolean = useRewardsWorth?.let { it > 0 } ?: false

    override fun getSelectedPaymentModes(): List<PaymentGatewayV1.PaymentInstrument> = paymentModes

    override fun getBuyerId(): String = buyerOrgId

    override fun getDeliveryOrgUnitId(): String = buyerOrgUnitId

    override fun getReqAdditionalData(): AdditionalData = additionalData

    // In case of checkout this is not required, checkout has to figure out category group from request
    override fun getReqCategoryGroupId(): String = CategoryGroupV2.Unknown.id

    override fun getReqCouponIds(): List<String> = couponIds

    override fun getReqPlatformId(): String {
        return platformId.name
    }

    fun copy(
        platformId: com.udaan.proto.models.ModelV1.SellingPlatform,
        buyerOrgId: String,
        requester: Requester,
        products: List<ItemProduct>,
        buyerOrgUnitId: String,
        billToOrgUnitId: String?,
        useRewardsWorth: Long?,
        selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
        paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
        deliverySlotId: String?,
        couponIds: List<String>,
        additionalData: AdditionalData,
        instruction: String?,
        itemLevelDeliverySlot: Map<String, String>?
    ) = CheckoutReqDto(
        platformId = platformId,
        buyerOrgId = buyerOrgId,
        requester = requester,
        products = products,
        buyerOrgUnitId = buyerOrgUnitId,
        billToOrgUnitId = billToOrgUnitId,
        useRewardsWorth = useRewardsWorth,
        selectedOnlinePaymentType = selectedOnlinePaymentType,
        paymentModes = paymentModes,
        deliverySlotId = deliverySlotId,
        couponIds = couponIds,
        additionalData = additionalData,
        instruction = instruction,
        itemLevelDeliverySlot = itemLevelDeliverySlot,
        deliveryChargeCartIdentifier = null
    )

    fun copy(
        platformId: com.udaan.proto.models.ModelV1.SellingPlatform,
        buyerOrgId: String,
        requester: Requester,
        products: List<ItemProduct>,
        buyerOrgUnitId: String,
        billToOrgUnitId: String?,
        useRewardsWorth: Long?,
        selectedOnlinePaymentType: SellerOrderInternals.ExtraData.SelectedOnlinePaymentType,
        paymentModes: List<PaymentGatewayV1.PaymentInstrument>,
        deliverySlotId: String?,
        couponIds: List<String>,
        additionalData: AdditionalData,
        instruction: String?,
        itemLevelDeliverySlot: Map<String, String>?,
        deliveryChargeCartIdentifier: String?
    ) = CheckoutReqDto(
        platformId = platformId,
        buyerOrgId = buyerOrgId,
        requester = requester,
        products = products,
        buyerOrgUnitId = buyerOrgUnitId,
        billToOrgUnitId = billToOrgUnitId,
        useRewardsWorth = useRewardsWorth,
        selectedOnlinePaymentType = selectedOnlinePaymentType,
        paymentModes = paymentModes,
        deliverySlotId = deliverySlotId,
        couponIds = couponIds,
        additionalData = additionalData,
        instruction = instruction,
        itemLevelDeliverySlot = itemLevelDeliverySlot,
        deliveryChargeCartIdentifier = deliveryChargeCartIdentifier
    )
}

data class OrderSLA(val orderId: String, val sla: Long)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CheckoutResDto(
    val placedOrderIds: List<String>,
    val prePaymentId: String? = null,
    val orderSlasMap: Map<String, OrderSLA>? = null,
    val mainOrderId: String? = null,
) : BaseResponseDto
